#!/usr/bin/env python3
"""
Test script to validate Isotonic Regression and ROC threshold fixes:
1. Sorted data for Isotonic Regression training
2. Calibrated predictions for ROC threshold calculation
3. Proper numpy array input for isotonic prediction
"""

import numpy as np
import sys
from pathlib import Path

def test_platt_scaling_fix():
    """Test the Platt scaling calibration approach"""
    try:
        from sklearn.linear_model import LogisticRegression

        # Test Case 1: Problematic data with many duplicates
        print("📋 Test Case 1: Duplicate-heavy data with Platt scaling")
        raw_predictions = np.array([0.9, 0.9, 0.8, 0.8, 0.7, 0.7, 0.3, 0.3, 0.2, 0.2])
        y_true = np.array([1, 0, 1, 0, 1, 0, 0, 1, 0, 1])

        # Check variance and unique values
        pred_variance = np.var(raw_predictions)
        unique_preds = len(np.unique(raw_predictions))
        print(f"   Variance: {pred_variance:.6f}, Unique values: {unique_preds}")

        # Apply Platt scaling (more robust for small datasets)
        if pred_variance > 1e-6 and unique_preds >= 5:
            platt_calibrator = LogisticRegression(max_iter=1000)
            platt_calibrator.fit(raw_predictions.reshape(-1, 1), y_true)

            # Test calibrated predictions
            test_values = np.array([0.3, 0.5, 0.8])
            calibrated_preds = platt_calibrator.predict_proba(test_values.reshape(-1, 1))[:, 1]

            print(f"   Raw predictions: {test_values}")
            print(f"   Calibrated predictions: {calibrated_preds}")

            # Should show meaningful variation (not all 0.5)
            pred_range = np.max(calibrated_preds) - np.min(calibrated_preds)
            assert pred_range > 0.1, f"Platt scaling should show variation, got range: {pred_range:.4f}"

            # Should not all be exactly 0.5
            not_half = np.sum(np.abs(calibrated_preds - 0.5) > 0.05)
            assert not_half >= 2, "At least 2 predictions should differ significantly from 0.5"

            print("✅ Platt scaling handles duplicate data successfully")
        else:
            print("   Insufficient variance - would use default threshold")

        # Test Case 2: Well-distributed data
        print("\n📋 Test Case 2: Well-distributed data with Platt scaling")
        np.random.seed(42)
        good_predictions = np.random.beta(2, 2, 50)  # Well-distributed between 0 and 1
        good_y_true = (good_predictions > 0.5).astype(int)

        good_variance = np.var(good_predictions)
        good_unique = len(np.unique(good_predictions))
        print(f"   Variance: {good_variance:.6f}, Unique values: {good_unique}")

        platt_good = LogisticRegression(max_iter=1000)
        platt_good.fit(good_predictions.reshape(-1, 1), good_y_true)
        good_pred = platt_good.predict_proba(np.array([0.3, 0.7]).reshape(-1, 1))[:, 1]

        print(f"   Predictions for [0.3, 0.7]: {good_pred}")

        # Should show meaningful variation
        pred_range = np.max(good_pred) - np.min(good_pred)
        assert pred_range > 0.1, "Good data should show prediction variation"
        print("✅ Platt scaling works with good data")

        return True

    except Exception as e:
        print(f"❌ Platt scaling test failed: {e}")
        return False

def test_roc_threshold_calibration_fix():
    """Test that ROC threshold uses calibrated predictions"""
    try:
        from sklearn.isotonic import IsotonicRegression
        from sklearn.metrics import roc_curve
        
        # Simulate validation data
        raw_predictions = np.array([0.9, 0.8, 0.7, 0.6, 0.4, 0.3, 0.2, 0.1])
        y_true = np.array([1, 1, 0, 1, 0, 0, 0, 0])
        
        # Sort data for proper isotonic fitting
        sort_idx = np.argsort(raw_predictions)
        sorted_raw_preds = raw_predictions[sort_idx]
        sorted_y_true = y_true[sort_idx]
        
        # Train isotonic calibrator
        iso_calibrator = IsotonicRegression(out_of_bounds='clip')
        iso_calibrator.fit(sorted_raw_preds, sorted_y_true)
        
        # Test BUGGY approach (uncalibrated threshold)
        fpr_bug, tpr_bug, thresh_bug = roc_curve(sorted_y_true, sorted_raw_preds)
        buggy_threshold = thresh_bug[(tpr_bug - fpr_bug).argmax()]
        
        # Test FIXED approach (calibrated threshold)
        calibrated_preds = iso_calibrator.predict(sorted_raw_preds)
        fpr_fix, tpr_fix, thresh_fix = roc_curve(sorted_y_true, calibrated_preds)
        fixed_threshold = thresh_fix[(tpr_fix - fpr_fix).argmax()]
        
        print(f"✅ ROC threshold calibration test:")
        print(f"   Buggy (uncalibrated): {buggy_threshold:.4f}")
        print(f"   Fixed (calibrated): {fixed_threshold:.4f}")
        
        # Thresholds should be different
        assert abs(buggy_threshold - fixed_threshold) > 0.01, "Calibrated threshold should differ"
        print("✅ ROC threshold uses calibrated predictions")
        return True
        
    except Exception as e:
        print(f"❌ ROC threshold test failed: {e}")
        return False

def test_numpy_array_input_fix():
    """Test that numpy array input prevents dtype=object issues"""
    try:
        from sklearn.isotonic import IsotonicRegression
        
        # Train a simple isotonic regressor
        X = np.array([0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9])
        y = np.array([0, 0, 0, 0, 1, 1, 1, 1, 1])
        
        iso_reg = IsotonicRegression(out_of_bounds='clip')
        iso_reg.fit(X, y)
        
        # Test BUGGY approach (Python list input)
        test_val = 0.75
        buggy_pred = iso_reg.predict([test_val])  # List input
        
        # Test FIXED approach (numpy array input)
        fixed_pred = iso_reg.predict(np.array([test_val]))  # Numpy array input
        
        print(f"✅ Numpy array input test:")
        print(f"   List input: {buggy_pred[0]:.4f}")
        print(f"   Array input: {fixed_pred[0]:.4f}")
        
        # Both should give same result, but array input is safer
        assert abs(buggy_pred[0] - fixed_pred[0]) < 0.0001, "Both inputs should give same result"
        
        # Test that the prediction is reasonable (not stuck at 0.5)
        assert abs(fixed_pred[0] - 0.5) > 0.1, "Prediction should not be stuck at 0.5"
        print("✅ Numpy array input works correctly")
        return True
        
    except Exception as e:
        print(f"❌ Numpy array input test failed: {e}")
        return False

def test_prediction_variation():
    """Test that predictions show meaningful variation"""
    try:
        from sklearn.isotonic import IsotonicRegression
        
        # Create training data with clear pattern
        X_train = np.linspace(0, 1, 20)
        y_train = (X_train > 0.5).astype(int)  # Step function at 0.5
        
        # Train isotonic regressor
        iso_reg = IsotonicRegression(out_of_bounds='clip')
        iso_reg.fit(X_train, y_train)
        
        # Test predictions at different points
        test_points = np.array([0.2, 0.4, 0.6, 0.8])
        predictions = iso_reg.predict(test_points)
        
        print(f"✅ Prediction variation test:")
        for i, (point, pred) in enumerate(zip(test_points, predictions)):
            print(f"   Input {point:.1f}: {pred:.4f}")
        
        # Predictions should vary significantly
        pred_range = np.max(predictions) - np.min(predictions)
        assert pred_range > 0.2, f"Prediction range too small: {pred_range:.4f}"
        
        # Should not all be 0.5
        not_half = np.sum(np.abs(predictions - 0.5) > 0.1)
        assert not_half >= 2, "At least 2 predictions should differ significantly from 0.5"
        
        print("✅ Predictions show meaningful variation")
        return True
        
    except Exception as e:
        print(f"❌ Prediction variation test failed: {e}")
        return False

def main():
    """Run all isotonic regression fix tests"""
    print("🔧 Testing Isotonic Regression and ROC Threshold Fixes\n")
    
    tests = [
        ("Platt Scaling Fix", test_platt_scaling_fix),
        ("ROC Threshold Calibration Fix", test_roc_threshold_calibration_fix),
        ("Numpy Array Input Fix", test_numpy_array_input_fix),
        ("Prediction Variation", test_prediction_variation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All isotonic regression fixes validated!")
        print("✅ Predictions should now vary meaningfully from 0.5000")
        print("✅ Binary classification should produce both 0 and 1 labels")
        print("✅ Calibrated probabilities should reflect model confidence")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
