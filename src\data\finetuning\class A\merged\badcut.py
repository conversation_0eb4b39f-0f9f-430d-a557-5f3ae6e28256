# badcut.py
from pathlib import Path

# ── 절대 경로 ─────────────────────────────────────────────
BASE_DIR = Path(r"C:\Users\<USER>\Desktop\home\ubuntu\financial_dashboard\src\data\finetuning\class A\merged")

SRC = BASE_DIR / "corrected_dataset_910.jsonl"     # 원본
DST = BASE_DIR / "complete_dataset_905.jsonl"     # 새 파일

# 삭제할 줄 번호(1-based)
BAD_LINES = {179, 220, 221, 237, 441}

# ── 실행 ────────────────────────────────────────────────
removed = 0
with SRC.open(encoding="utf-8") as inp, DST.open("w", encoding="utf-8") as out:
    for idx, line in enumerate(inp, 1):
        if idx in BAD_LINES:
            removed += 1          # 건너뛰기
            continue
        out.write(line)

print(f"✅ 완료: {removed}줄 제거 → '{DST.name}' 저장\n경로: {DST}")
