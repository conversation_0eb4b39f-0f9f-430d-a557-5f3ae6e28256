"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/market_data";
exports.ids = ["pages/api/market_data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmarket_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmarket_data.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmarket_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmarket_data.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_market_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\market_data.ts */ \"(api)/./src/pages/api/market_data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_market_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_market_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/market_data\",\n        pathname: \"/api/market_data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_market_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRm1hcmtldF9kYXRhJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNhcGklNUNtYXJrZXRfZGF0YS50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDRTtBQUMxRDtBQUM4RDtBQUM5RDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsMERBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLDBEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLHlHQUFtQjtBQUNsRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vc3JjXFxcXHBhZ2VzXFxcXGFwaVxcXFxtYXJrZXRfZGF0YS50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCAnZGVmYXVsdCcpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgJ2NvbmZpZycpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbWFya2V0X2RhdGFcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9tYXJrZXRfZGF0YVwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmarket_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmarket_data.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/market_data.ts":
/*!**************************************!*\
  !*** ./src/pages/api/market_data.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// src/pages/api/market_data.ts\n// 시장 지표 심볼 매핑\nconst MARKET_SYMBOLS = {\n    '^GSPC': 'S&P 500',\n    '^IXIC': '나스닥',\n    '^DJI': '다우존스',\n    '^VIX': 'VIX',\n    'KRW=X': '달러/원' // USD/KRW\n};\n// yfinance 데이터를 가져오는 함수\nasync function fetchYahooFinanceData(symbol) {\n    try {\n        // Yahoo Finance API 사용 (무료 버전)\n        const response = await fetch(`https://query1.finance.yahoo.com/v8/finance/chart/${symbol}?interval=1m&range=1d`, {\n            headers: {\n                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        return data;\n    } catch (error) {\n        console.error(`Error fetching data for ${symbol}:`, error);\n        return null;\n    }\n}\n// 시장 데이터 파싱 함수\nfunction parseMarketData(symbol, data) {\n    try {\n        if (!data?.chart?.result?.[0]) {\n            return null;\n        }\n        const result = data.chart.result[0];\n        const meta = result.meta;\n        const currentPrice = meta.regularMarketPrice || meta.previousClose;\n        const previousClose = meta.previousClose;\n        const change = currentPrice - previousClose;\n        const changePercent = change / previousClose * 100;\n        return {\n            symbol,\n            label: MARKET_SYMBOLS[symbol] || symbol,\n            price: currentPrice,\n            change,\n            changePercent,\n            trend: change > 0 ? 'up' : change < 0 ? 'down' : 'neutral'\n        };\n    } catch (error) {\n        console.error(`Error parsing data for ${symbol}:`, error);\n        return null;\n    }\n}\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const symbols = Object.keys(MARKET_SYMBOLS);\n        const marketData = [];\n        // 모든 심볼에 대해 데이터 가져오기\n        const promises = symbols.map(async (symbol)=>{\n            const data = await fetchYahooFinanceData(symbol);\n            if (data) {\n                const parsed = parseMarketData(symbol, data);\n                if (parsed) {\n                    marketData.push(parsed);\n                }\n            }\n        });\n        await Promise.all(promises);\n        // 데이터가 없으면 fallback 데이터 사용\n        if (marketData.length === 0) {\n            console.log('No market data available, using fallback data');\n            return res.status(200).json({\n                success: true,\n                data: [\n                    {\n                        symbol: '^GSPC',\n                        label: 'S&P 500',\n                        price: 4567.89,\n                        change: 12.34,\n                        changePercent: 0.27,\n                        trend: 'up'\n                    },\n                    {\n                        symbol: '^IXIC',\n                        label: '나스닥',\n                        price: 14234.56,\n                        change: -45.67,\n                        changePercent: -0.32,\n                        trend: 'down'\n                    },\n                    {\n                        symbol: '^DJI',\n                        label: '다우존스',\n                        price: 34567.12,\n                        change: 89.45,\n                        changePercent: 0.26,\n                        trend: 'up'\n                    },\n                    {\n                        symbol: '^VIX',\n                        label: 'VIX',\n                        price: 18.45,\n                        change: -1.23,\n                        changePercent: -6.25,\n                        trend: 'down'\n                    },\n                    {\n                        symbol: 'KRW=X',\n                        label: '달러/원',\n                        price: 1327.50,\n                        change: 5.25,\n                        changePercent: 0.40,\n                        trend: 'up'\n                    }\n                ],\n                timestamp: new Date().toISOString(),\n                source: 'fallback'\n            });\n        }\n        res.status(200).json({\n            success: true,\n            data: marketData,\n            timestamp: new Date().toISOString(),\n            source: 'yahoo_finance'\n        });\n    } catch (error) {\n        console.error('Market data API error:', error);\n        res.status(500).json({\n            error: 'Failed to fetch market data',\n            message: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvcGFnZXMvYXBpL21hcmtldF9kYXRhLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSwrQkFBK0I7QUFZL0IsY0FBYztBQUNkLE1BQU1BLGlCQUFpQjtJQUNyQixTQUFTO0lBQ1QsU0FBUztJQUNULFFBQVE7SUFDUixRQUFRO0lBQ1IsU0FBUyxPQUFjLFVBQVU7QUFDbkM7QUFFQSx3QkFBd0I7QUFDeEIsZUFBZUMsc0JBQXNCQyxNQUFjO0lBQ2pELElBQUk7UUFDRiwrQkFBK0I7UUFDL0IsTUFBTUMsV0FBVyxNQUFNQyxNQUNyQixDQUFDLGtEQUFrRCxFQUFFRixPQUFPLHFCQUFxQixDQUFDLEVBQ2xGO1lBQ0VHLFNBQVM7Z0JBQ1AsY0FBYztZQUNoQjtRQUNGO1FBR0YsSUFBSSxDQUFDRixTQUFTRyxFQUFFLEVBQUU7WUFDaEIsTUFBTSxJQUFJQyxNQUFNLENBQUMsb0JBQW9CLEVBQUVKLFNBQVNLLE1BQU0sRUFBRTtRQUMxRDtRQUVBLE1BQU1DLE9BQU8sTUFBTU4sU0FBU08sSUFBSTtRQUNoQyxPQUFPRDtJQUNULEVBQUUsT0FBT0UsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsQ0FBQyx3QkFBd0IsRUFBRVQsT0FBTyxDQUFDLENBQUMsRUFBRVM7UUFDcEQsT0FBTztJQUNUO0FBQ0Y7QUFFQSxlQUFlO0FBQ2YsU0FBU0UsZ0JBQWdCWCxNQUFjLEVBQUVPLElBQVM7SUFDaEQsSUFBSTtRQUNGLElBQUksQ0FBQ0EsTUFBTUssT0FBT0MsUUFBUSxDQUFDLEVBQUUsRUFBRTtZQUM3QixPQUFPO1FBQ1Q7UUFFQSxNQUFNQSxTQUFTTixLQUFLSyxLQUFLLENBQUNDLE1BQU0sQ0FBQyxFQUFFO1FBQ25DLE1BQU1DLE9BQU9ELE9BQU9DLElBQUk7UUFFeEIsTUFBTUMsZUFBZUQsS0FBS0Usa0JBQWtCLElBQUlGLEtBQUtHLGFBQWE7UUFDbEUsTUFBTUEsZ0JBQWdCSCxLQUFLRyxhQUFhO1FBQ3hDLE1BQU1DLFNBQVNILGVBQWVFO1FBQzlCLE1BQU1FLGdCQUFnQixTQUFVRixnQkFBaUI7UUFFakQsT0FBTztZQUNMakI7WUFDQW9CLE9BQU90QixjQUFjLENBQUNFLE9BQXNDLElBQUlBO1lBQ2hFcUIsT0FBT047WUFDUEc7WUFDQUM7WUFDQUcsT0FBT0osU0FBUyxJQUFJLE9BQU9BLFNBQVMsSUFBSSxTQUFTO1FBQ25EO0lBQ0YsRUFBRSxPQUFPVCxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxDQUFDLHVCQUF1QixFQUFFVCxPQUFPLENBQUMsQ0FBQyxFQUFFUztRQUNuRCxPQUFPO0lBQ1Q7QUFDRjtBQUVlLGVBQWVjLFFBQVFDLEdBQW1CLEVBQUVDLEdBQW9CO0lBQzdFLElBQUlELElBQUlFLE1BQU0sS0FBSyxPQUFPO1FBQ3hCLE9BQU9ELElBQUluQixNQUFNLENBQUMsS0FBS0UsSUFBSSxDQUFDO1lBQUVDLE9BQU87UUFBcUI7SUFDNUQ7SUFFQSxJQUFJO1FBQ0YsTUFBTWtCLFVBQVVDLE9BQU9DLElBQUksQ0FBQy9CO1FBQzVCLE1BQU1nQyxhQUEyQixFQUFFO1FBRW5DLHFCQUFxQjtRQUNyQixNQUFNQyxXQUFXSixRQUFRSyxHQUFHLENBQUMsT0FBT2hDO1lBQ2xDLE1BQU1PLE9BQU8sTUFBTVIsc0JBQXNCQztZQUN6QyxJQUFJTyxNQUFNO2dCQUNSLE1BQU0wQixTQUFTdEIsZ0JBQWdCWCxRQUFRTztnQkFDdkMsSUFBSTBCLFFBQVE7b0JBQ1ZILFdBQVdJLElBQUksQ0FBQ0Q7Z0JBQ2xCO1lBQ0Y7UUFDRjtRQUVBLE1BQU1FLFFBQVFDLEdBQUcsQ0FBQ0w7UUFFbEIsMkJBQTJCO1FBQzNCLElBQUlELFdBQVdPLE1BQU0sS0FBSyxHQUFHO1lBQzNCM0IsUUFBUTRCLEdBQUcsQ0FBQztZQUNaLE9BQU9iLElBQUluQixNQUFNLENBQUMsS0FBS0UsSUFBSSxDQUFDO2dCQUMxQitCLFNBQVM7Z0JBQ1RoQyxNQUFNO29CQUNKO3dCQUFFUCxRQUFRO3dCQUFTb0IsT0FBTzt3QkFBV0MsT0FBTzt3QkFBU0gsUUFBUTt3QkFBT0MsZUFBZTt3QkFBTUcsT0FBTztvQkFBSztvQkFDckc7d0JBQUV0QixRQUFRO3dCQUFTb0IsT0FBTzt3QkFBT0MsT0FBTzt3QkFBVUgsUUFBUSxDQUFDO3dCQUFPQyxlQUFlLENBQUM7d0JBQU1HLE9BQU87b0JBQU87b0JBQ3RHO3dCQUFFdEIsUUFBUTt3QkFBUW9CLE9BQU87d0JBQVFDLE9BQU87d0JBQVVILFFBQVE7d0JBQU9DLGVBQWU7d0JBQU1HLE9BQU87b0JBQUs7b0JBQ2xHO3dCQUFFdEIsUUFBUTt3QkFBUW9CLE9BQU87d0JBQU9DLE9BQU87d0JBQU9ILFFBQVEsQ0FBQzt3QkFBTUMsZUFBZSxDQUFDO3dCQUFNRyxPQUFPO29CQUFPO29CQUNqRzt3QkFBRXRCLFFBQVE7d0JBQVNvQixPQUFPO3dCQUFRQyxPQUFPO3dCQUFTSCxRQUFRO3dCQUFNQyxlQUFlO3dCQUFNRyxPQUFPO29CQUFLO2lCQUNsRztnQkFDRGtCLFdBQVcsSUFBSUMsT0FBT0MsV0FBVztnQkFDakNDLFFBQVE7WUFDVjtRQUNGO1FBRUFsQixJQUFJbkIsTUFBTSxDQUFDLEtBQUtFLElBQUksQ0FBQztZQUNuQitCLFNBQVM7WUFDVGhDLE1BQU11QjtZQUNOVSxXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDakNDLFFBQVE7UUFDVjtJQUVGLEVBQUUsT0FBT2xDLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7UUFDeENnQixJQUFJbkIsTUFBTSxDQUFDLEtBQUtFLElBQUksQ0FBQztZQUNuQkMsT0FBTztZQUNQbUMsU0FBU25DLGlCQUFpQkosUUFBUUksTUFBTW1DLE9BQU8sR0FBRztRQUNwRDtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29uZzdcXERlc2t0b3BcXGhvbWVcXHVidW50dVxcZmluYW5jaWFsX2Rhc2hib2FyZFxcc3JjXFxwYWdlc1xcYXBpXFxtYXJrZXRfZGF0YS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcGFnZXMvYXBpL21hcmtldF9kYXRhLnRzXG5pbXBvcnQgeyBOZXh0QXBpUmVxdWVzdCwgTmV4dEFwaVJlc3BvbnNlIH0gZnJvbSAnbmV4dCc7XG5cbmludGVyZmFjZSBNYXJrZXREYXRhIHtcbiAgc3ltYm9sOiBzdHJpbmc7XG4gIGxhYmVsOiBzdHJpbmc7XG4gIHByaWNlOiBudW1iZXI7XG4gIGNoYW5nZTogbnVtYmVyO1xuICBjaGFuZ2VQZXJjZW50OiBudW1iZXI7XG4gIHRyZW5kOiAndXAnIHwgJ2Rvd24nIHwgJ25ldXRyYWwnO1xufVxuXG4vLyDsi5zsnqUg7KeA7ZGcIOyLrOuzvCDrp6TtlZFcbmNvbnN0IE1BUktFVF9TWU1CT0xTID0ge1xuICAnXkdTUEMnOiAnUyZQIDUwMCcsICAgICAgLy8gUyZQIDUwMFxuICAnXklYSUMnOiAn64KY7Iqk64ulJywgICAgICAgIC8vIE5BU0RBUVxuICAnXkRKSSc6ICfri6TsmrDsobTsiqQnLCAgICAgICAvLyBEb3cgSm9uZXNcbiAgJ15WSVgnOiAnVklYJywgICAgICAgICAgIC8vIFZJWFxuICAnS1JXPVgnOiAn64us65+sL+ybkCcgICAgICAgIC8vIFVTRC9LUldcbn07XG5cbi8vIHlmaW5hbmNlIOuNsOydtO2EsOulvCDqsIDsoLjsmKTripQg7ZWo7IiYXG5hc3luYyBmdW5jdGlvbiBmZXRjaFlhaG9vRmluYW5jZURhdGEoc3ltYm9sOiBzdHJpbmcpOiBQcm9taXNlPGFueT4ge1xuICB0cnkge1xuICAgIC8vIFlhaG9vIEZpbmFuY2UgQVBJIOyCrOyaqSAo66y066OMIOuyhOyghClcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFxuICAgICAgYGh0dHBzOi8vcXVlcnkxLmZpbmFuY2UueWFob28uY29tL3Y4L2ZpbmFuY2UvY2hhcnQvJHtzeW1ib2x9P2ludGVydmFsPTFtJnJhbmdlPTFkYCxcbiAgICAgIHtcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdVc2VyLUFnZW50JzogJ01vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNidcbiAgICAgICAgfVxuICAgICAgfVxuICAgICk7XG4gICAgXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIHJldHVybiBkYXRhO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGZldGNoaW5nIGRhdGEgZm9yICR7c3ltYm9sfTpgLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuLy8g7Iuc7J6lIOuNsOydtO2EsCDtjIzsi7Eg7ZWo7IiYXG5mdW5jdGlvbiBwYXJzZU1hcmtldERhdGEoc3ltYm9sOiBzdHJpbmcsIGRhdGE6IGFueSk6IE1hcmtldERhdGEgfCBudWxsIHtcbiAgdHJ5IHtcbiAgICBpZiAoIWRhdGE/LmNoYXJ0Py5yZXN1bHQ/LlswXSkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgY29uc3QgcmVzdWx0ID0gZGF0YS5jaGFydC5yZXN1bHRbMF07XG4gICAgY29uc3QgbWV0YSA9IHJlc3VsdC5tZXRhO1xuICAgIFxuICAgIGNvbnN0IGN1cnJlbnRQcmljZSA9IG1ldGEucmVndWxhck1hcmtldFByaWNlIHx8IG1ldGEucHJldmlvdXNDbG9zZTtcbiAgICBjb25zdCBwcmV2aW91c0Nsb3NlID0gbWV0YS5wcmV2aW91c0Nsb3NlO1xuICAgIGNvbnN0IGNoYW5nZSA9IGN1cnJlbnRQcmljZSAtIHByZXZpb3VzQ2xvc2U7XG4gICAgY29uc3QgY2hhbmdlUGVyY2VudCA9IChjaGFuZ2UgLyBwcmV2aW91c0Nsb3NlKSAqIDEwMDtcblxuICAgIHJldHVybiB7XG4gICAgICBzeW1ib2wsXG4gICAgICBsYWJlbDogTUFSS0VUX1NZTUJPTFNbc3ltYm9sIGFzIGtleW9mIHR5cGVvZiBNQVJLRVRfU1lNQk9MU10gfHwgc3ltYm9sLFxuICAgICAgcHJpY2U6IGN1cnJlbnRQcmljZSxcbiAgICAgIGNoYW5nZSxcbiAgICAgIGNoYW5nZVBlcmNlbnQsXG4gICAgICB0cmVuZDogY2hhbmdlID4gMCA/ICd1cCcgOiBjaGFuZ2UgPCAwID8gJ2Rvd24nIDogJ25ldXRyYWwnXG4gICAgfTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKGBFcnJvciBwYXJzaW5nIGRhdGEgZm9yICR7c3ltYm9sfTpgLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gaGFuZGxlcihyZXE6IE5leHRBcGlSZXF1ZXN0LCByZXM6IE5leHRBcGlSZXNwb25zZSkge1xuICBpZiAocmVxLm1ldGhvZCAhPT0gJ0dFVCcpIHtcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDUpLmpzb24oeyBlcnJvcjogJ01ldGhvZCBub3QgYWxsb3dlZCcgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IHN5bWJvbHMgPSBPYmplY3Qua2V5cyhNQVJLRVRfU1lNQk9MUyk7XG4gICAgY29uc3QgbWFya2V0RGF0YTogTWFya2V0RGF0YVtdID0gW107XG5cbiAgICAvLyDrqqjrk6Ag7Ius67O87JeQIOuMgO2VtCDrjbDsnbTthLAg6rCA7KC47Jik6riwXG4gICAgY29uc3QgcHJvbWlzZXMgPSBzeW1ib2xzLm1hcChhc3luYyAoc3ltYm9sKSA9PiB7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgZmV0Y2hZYWhvb0ZpbmFuY2VEYXRhKHN5bWJvbCk7XG4gICAgICBpZiAoZGF0YSkge1xuICAgICAgICBjb25zdCBwYXJzZWQgPSBwYXJzZU1hcmtldERhdGEoc3ltYm9sLCBkYXRhKTtcbiAgICAgICAgaWYgKHBhcnNlZCkge1xuICAgICAgICAgIG1hcmtldERhdGEucHVzaChwYXJzZWQpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBhd2FpdCBQcm9taXNlLmFsbChwcm9taXNlcyk7XG5cbiAgICAvLyDrjbDsnbTthLDqsIAg7JeG7Jy866m0IGZhbGxiYWNrIOuNsOydtO2EsCDsgqzsmqlcbiAgICBpZiAobWFya2V0RGF0YS5sZW5ndGggPT09IDApIHtcbiAgICAgIGNvbnNvbGUubG9nKCdObyBtYXJrZXQgZGF0YSBhdmFpbGFibGUsIHVzaW5nIGZhbGxiYWNrIGRhdGEnKTtcbiAgICAgIHJldHVybiByZXMuc3RhdHVzKDIwMCkuanNvbih7XG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgIGRhdGE6IFtcbiAgICAgICAgICB7IHN5bWJvbDogJ15HU1BDJywgbGFiZWw6ICdTJlAgNTAwJywgcHJpY2U6IDQ1NjcuODksIGNoYW5nZTogMTIuMzQsIGNoYW5nZVBlcmNlbnQ6IDAuMjcsIHRyZW5kOiAndXAnIH0sXG4gICAgICAgICAgeyBzeW1ib2w6ICdeSVhJQycsIGxhYmVsOiAn64KY7Iqk64ulJywgcHJpY2U6IDE0MjM0LjU2LCBjaGFuZ2U6IC00NS42NywgY2hhbmdlUGVyY2VudDogLTAuMzIsIHRyZW5kOiAnZG93bicgfSxcbiAgICAgICAgICB7IHN5bWJvbDogJ15ESkknLCBsYWJlbDogJ+uLpOyasOyhtOyKpCcsIHByaWNlOiAzNDU2Ny4xMiwgY2hhbmdlOiA4OS40NSwgY2hhbmdlUGVyY2VudDogMC4yNiwgdHJlbmQ6ICd1cCcgfSxcbiAgICAgICAgICB7IHN5bWJvbDogJ15WSVgnLCBsYWJlbDogJ1ZJWCcsIHByaWNlOiAxOC40NSwgY2hhbmdlOiAtMS4yMywgY2hhbmdlUGVyY2VudDogLTYuMjUsIHRyZW5kOiAnZG93bicgfSxcbiAgICAgICAgICB7IHN5bWJvbDogJ0tSVz1YJywgbGFiZWw6ICfri6zrn6wv7JuQJywgcHJpY2U6IDEzMjcuNTAsIGNoYW5nZTogNS4yNSwgY2hhbmdlUGVyY2VudDogMC40MCwgdHJlbmQ6ICd1cCcgfVxuICAgICAgICBdLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgc291cmNlOiAnZmFsbGJhY2snXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXMuc3RhdHVzKDIwMCkuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZGF0YTogbWFya2V0RGF0YSxcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgc291cmNlOiAneWFob29fZmluYW5jZSdcbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ01hcmtldCBkYXRhIEFQSSBlcnJvcjonLCBlcnJvcik7XG4gICAgcmVzLnN0YXR1cyg1MDApLmpzb24oeyBcbiAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIG1hcmtldCBkYXRhJyxcbiAgICAgIG1lc3NhZ2U6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgfSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJNQVJLRVRfU1lNQk9MUyIsImZldGNoWWFob29GaW5hbmNlRGF0YSIsInN5bWJvbCIsInJlc3BvbnNlIiwiZmV0Y2giLCJoZWFkZXJzIiwib2siLCJFcnJvciIsInN0YXR1cyIsImRhdGEiLCJqc29uIiwiZXJyb3IiLCJjb25zb2xlIiwicGFyc2VNYXJrZXREYXRhIiwiY2hhcnQiLCJyZXN1bHQiLCJtZXRhIiwiY3VycmVudFByaWNlIiwicmVndWxhck1hcmtldFByaWNlIiwicHJldmlvdXNDbG9zZSIsImNoYW5nZSIsImNoYW5nZVBlcmNlbnQiLCJsYWJlbCIsInByaWNlIiwidHJlbmQiLCJoYW5kbGVyIiwicmVxIiwicmVzIiwibWV0aG9kIiwic3ltYm9scyIsIk9iamVjdCIsImtleXMiLCJtYXJrZXREYXRhIiwicHJvbWlzZXMiLCJtYXAiLCJwYXJzZWQiLCJwdXNoIiwiUHJvbWlzZSIsImFsbCIsImxlbmd0aCIsImxvZyIsInN1Y2Nlc3MiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzb3VyY2UiLCJtZXNzYWdlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/market_data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fmarket_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cmarket_data.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();