{"cells": [{"cell_type": "markdown", "metadata": {"id": "KlyhGsph2Fid"}, "source": ["# 딥러닝분석_(1)LSTM주가분석"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "qkNo_axwpfmF", "outputId": "abd618cf-228a-4129-d445-7f5ea2d2022b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: yfinance in /usr/local/lib/python3.11/dist-packages (0.2.61)\n", "Requirement already satisfied: pandas>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from yfinance) (2.2.2)\n", "Requirement already satisfied: numpy>=1.16.5 in /usr/local/lib/python3.11/dist-packages (from yfinance) (2.0.2)\n", "Requirement already satisfied: requests>=2.31 in /usr/local/lib/python3.11/dist-packages (from yfinance) (2.32.3)\n", "Requirement already satisfied: multitasking>=0.0.7 in /usr/local/lib/python3.11/dist-packages (from yfinance) (0.0.11)\n", "Requirement already satisfied: platformdirs>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from yfinance) (4.3.8)\n", "Requirement already satisfied: pytz>=2022.5 in /usr/local/lib/python3.11/dist-packages (from yfinance) (2025.2)\n", "Requirement already satisfied: frozendict>=2.3.4 in /usr/local/lib/python3.11/dist-packages (from yfinance) (2.4.6)\n", "Requirement already satisfied: peewee>=3.16.2 in /usr/local/lib/python3.11/dist-packages (from yfinance) (3.18.1)\n", "Requirement already satisfied: beautifulsoup4>=4.11.1 in /usr/local/lib/python3.11/dist-packages (from yfinance) (4.13.4)\n", "Requirement already satisfied: curl_cffi>=0.7 in /usr/local/lib/python3.11/dist-packages (from yfinance) (0.11.1)\n", "Requirement already satisfied: protobuf>=3.19.0 in /usr/local/lib/python3.11/dist-packages (from yfinance) (5.29.5)\n", "Requirement already satisfied: websockets>=13.0 in /usr/local/lib/python3.11/dist-packages (from yfinance) (15.0.1)\n", "Requirement already satisfied: soupsieve>1.2 in /usr/local/lib/python3.11/dist-packages (from beautifulsoup4>=4.11.1->yfinance) (2.7)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from beautifulsoup4>=4.11.1->yfinance) (4.13.2)\n", "Requirement already satisfied: cffi>=1.12.0 in /usr/local/lib/python3.11/dist-packages (from curl_cffi>=0.7->yfinance) (1.17.1)\n", "Requirement already satisfied: certifi>=2024.2.2 in /usr/local/lib/python3.11/dist-packages (from curl_cffi>=0.7->yfinance) (2025.4.26)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.3.0->yfinance) (2.9.0.post0)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.3.0->yfinance) (2025.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.31->yfinance) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.31->yfinance) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.31->yfinance) (2.4.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.12.0->curl_cffi>=0.7->yfinance) (2.22)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas>=1.3.0->yfinance) (1.17.0)\n"]}], "source": ["# 주가 데이터를 다운로드 받기 위해서 야후 파이낸스 라이브러리 설치\n", "!pip install yfinance"]}, {"cell_type": "markdown", "metadata": {"id": "Xl6h19XGpfmG"}, "source": ["# 1. 데이터 수집"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ImgFQHvz2XBY"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import yfinance as yf"]}, {"cell_type": "markdown", "metadata": {"id": "qEnFut4qpfmG"}, "source": ["### - 기업 주가 데이터를 다운로드하여 데이터프레임으로 저장"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "f9T1DxySpfmG"}, "outputs": [], "source": ["name = 'GOOG'  #종목명 -구글\n", "\n", "start_day = '2021-01-01'\n", "end_day = '2024-08-01'"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5M6iNSi0pfmG", "outputId": "d7e0b15e-75ab-4ccb-f337-4d341b464309"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["YF.download() has changed argument auto_adjust default to True\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\r[*********************100%***********************]  1 of 1 completed\n"]}], "source": ["stock = yf.download(name, start=start_day, end=end_day)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 286}, "id": "utsc6Z1SpfmH", "outputId": "23de1570-4f3e-48c6-bdbc-e07187f96e02"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(899, 5)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["Price           Close       High        Low       Open    Volume\n", "Ticker           GOOG       GOOG       GOOG       GOOG      GOOG\n", "Date                                                            \n", "2021-01-04  86.004646  87.617506  84.989952  87.462736  38038000\n", "2021-01-05  86.635658  86.971569  85.495809  85.843413  22906000\n", "2021-01-06  86.355476  86.987979  84.549524  84.730171  52042000\n", "2021-01-07  88.941231  88.998455  86.443061  86.592849  45300000\n", "2021-01-08  89.934532  90.065413  88.258723  88.977570  41012000"], "text/html": ["\n", "  <div id=\"df-465c4ca2-d9e7-4c5f-9613-9d1af420672d\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th>Price</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>Volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticker</th>\n", "      <th>GOOG</th>\n", "      <th>GOOG</th>\n", "      <th>GOOG</th>\n", "      <th>GOOG</th>\n", "      <th>GOOG</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-01-04</th>\n", "      <td>86.004646</td>\n", "      <td>87.617506</td>\n", "      <td>84.989952</td>\n", "      <td>87.462736</td>\n", "      <td>38038000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-05</th>\n", "      <td>86.635658</td>\n", "      <td>86.971569</td>\n", "      <td>85.495809</td>\n", "      <td>85.843413</td>\n", "      <td>22906000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-06</th>\n", "      <td>86.355476</td>\n", "      <td>86.987979</td>\n", "      <td>84.549524</td>\n", "      <td>84.730171</td>\n", "      <td>52042000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-07</th>\n", "      <td>88.941231</td>\n", "      <td>88.998455</td>\n", "      <td>86.443061</td>\n", "      <td>86.592849</td>\n", "      <td>45300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-08</th>\n", "      <td>89.934532</td>\n", "      <td>90.065413</td>\n", "      <td>88.258723</td>\n", "      <td>88.977570</td>\n", "      <td>41012000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-465c4ca2-d9e7-4c5f-9613-9d1af420672d')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-465c4ca2-d9e7-4c5f-9613-9d1af420672d button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-465c4ca2-d9e7-4c5f-9613-9d1af420672d');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-27e6dc71-2786-47ca-9ecf-f8180c14182f\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-27e6dc71-2786-47ca-9ecf-f8180c14182f')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-27e6dc71-2786-47ca-9ecf-f8180c14182f button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "stock", "summary": "{\n  \"name\": \"stock\",\n  \"rows\": 899,\n  \"fields\": [\n    {\n      \"column\": [\n        \"Date\",\n        \"\"\n      ],\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2021-01-04 00:00:00\",\n        \"max\": \"2024-07-31 00:00:00\",\n        \"num_unique_values\": 899,\n        \"samples\": [\n          \"2022-04-28 00:00:00\",\n          \"2024-03-26 00:00:00\",\n          \"2023-12-18 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Close\",\n        \"GOOG\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.929696002707008,\n        \"min\": 83.0964126586914,\n        \"max\": 191.96998596191406,\n        \"num_unique_values\": 879,\n        \"samples\": [\n          114.42453002929688,\n          145.75762939453125,\n          148.038818359375\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"High\",\n        \"GOOG\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.98421966409163,\n        \"min\": 86.14199249274185,\n        \"max\": 192.61765188536268,\n        \"num_unique_values\": 899,\n        \"samples\": [\n          119.87073843519462,\n          152.4778074943312,\n          137.72765117577347\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Low\",\n        \"GOOG\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.871884275560717,\n        \"min\": 83.05660031387845,\n        \"max\": 189.9372837700379,\n        \"num_unique_values\": 899,\n        \"samples\": [\n          114.60109635829187,\n          150.31803876287827,\n          133.1393831198481\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Open\",\n        \"GOOG\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.97366026996111,\n        \"min\": 84.73017148371405,\n        \"max\": 191.06324032448242,\n        \"num_unique_values\": 899,\n        \"samples\": [\n          116.56290421913432,\n          150.527055498697,\n          133.22895519525449\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": [\n        \"Volume\",\n        \"GOOG\"\n      ],\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 10442699,\n        \"min\": 7409100,\n        \"max\": 97798600,\n        \"num_unique_values\": 893,\n        \"samples\": [\n          19517900,\n          17156200,\n          97798600\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 8}], "source": ["print(stock.shape)  #작업 확인용 출력\n", "stock.head() #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "Bdp8HwsipfmH"}, "source": ["# 2. 데이터 준비 및 탐색"]}, {"cell_type": "markdown", "metadata": {"id": "FEvQ_CDQpfmH"}, "source": ["### - 분석할 컬럼(날짜, 종가) 추출"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 237}, "id": "ekDosKCkpfmH", "outputId": "8a01180e-603a-4cef-c255-e6db2a7db04f"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Ticker           GOOG\n", "Date                 \n", "2021-01-04  86.004646\n", "2021-01-05  86.635658\n", "2021-01-06  86.355476\n", "2021-01-07  88.941231\n", "2021-01-08  89.934532"], "text/html": ["\n", "  <div id=\"df-587cbbe9-2653-4d96-b8d5-76568c83017b\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>Ticker</th>\n", "      <th>GOOG</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-01-04</th>\n", "      <td>86.004646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-05</th>\n", "      <td>86.635658</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-06</th>\n", "      <td>86.355476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-07</th>\n", "      <td>88.941231</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-01-08</th>\n", "      <td>89.934532</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-587cbbe9-2653-4d96-b8d5-76568c83017b')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-587cbbe9-2653-4d96-b8d5-76568c83017b button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-587cbbe9-2653-4d96-b8d5-76568c83017b');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-cbb9faa4-53c5-4b5b-b5c1-77d628c597f0\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cbb9faa4-53c5-4b5b-b5c1-77d628c597f0')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-cbb9faa4-53c5-4b5b-b5c1-77d628c597f0 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "stock2", "summary": "{\n  \"name\": \"stock2\",\n  \"rows\": 899,\n  \"fields\": [\n    {\n      \"column\": \"Date\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2021-01-04 00:00:00\",\n        \"max\": \"2024-07-31 00:00:00\",\n        \"num_unique_values\": 899,\n        \"samples\": [\n          \"2022-04-28 00:00:00\",\n          \"2024-03-26 00:00:00\",\n          \"2023-12-18 00:00:00\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"GOOG\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22.929696002707008,\n        \"min\": 83.0964126586914,\n        \"max\": 191.96998596191406,\n        \"num_unique_values\": 879,\n        \"samples\": [\n          114.42453002929688,\n          145.75762939453125,\n          148.038818359375\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 6}], "source": ["stock2 = pd.DataFrame(stock['Close'])\n", "\n", "stock2.head() #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "FEyBtTMbpfmI"}, "source": ["#### - 파일 저장"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ks8vuQUnpfmI"}, "outputs": [], "source": ["# 다운로드한 주가 데이터를 파일 저장\n", "stock2.to_csv('./'+name+'.csv')"]}, {"cell_type": "markdown", "metadata": {"id": "u8OiZ5nVpfmI"}, "source": ["### - 데이터 정규화"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "24z8TryYgx4d", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "230f1a2f-9f5a-40c4-fd21-013e60120d3d"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[86.0046463]\n"]}], "source": ["stock2.reset_index()\n", "stock_values = stock2.values\n", "\n", "print(stock_values[0]) #작업 확인용 출력"]}, {"cell_type": "code", "source": ["stock_values"], "metadata": {"id": "HN5uoJl54WP8"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "EISI9In0pfmI"}, "source": ["### - 0~1 범위로 스케일링"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AHmcVkilpfmI"}, "outputs": [], "source": ["from sklearn.preprocessing import MinMaxScaler"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6wNWbEetpfmI", "outputId": "c82fdc79-c844-4456-a512-eeb16c449ba3"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.02671203])"]}, "metadata": {}, "execution_count": 12}], "source": ["scaler = MinMaxScaler(feature_range=(0,1))\n", "stock_values_scaled=scaler.fit_transform(stock_values)\n", "\n", "stock_values_scaled[0] #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "pBn7dlINpfmI"}, "source": ["# 3. 분석 모델 구축"]}, {"cell_type": "markdown", "metadata": {"id": "FoU3jnfjpfmI"}, "source": ["## 3-1. 주가 분석 모델의 학습용 데이터 준비"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KFTYvSh8gzQ1", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b3bbccb8-c177-4535-cca7-26aaa11eeab3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["719 180\n"]}], "source": ["n_train = int(len(stock_values)*0.8)  #학습용 데이터 갯수\n", "n_test = len(stock_values) - n_train  #평가용 데이터 갯수\n", "\n", "print(n_train, n_test) #작업 확인용 출력"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "8HNGJj5ZpfmI", "outputId": "c465f5b4-0a81-4e9f-d416-2c97a80579d3"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.02671203, 0.03250785, 0.02993439, 0.05368445, 0.06280789,\n", "       0.04430047, 0.03508124, 0.03866933, 0.03216959, 0.03034587,\n", "       0.05533445, 0.0992329 , 0.10122109, 0.10570054, 0.10494639,\n", "       0.11310075, 0.07358582, 0.08835883, 0.07584842, 0.10583761])"]}, "metadata": {}, "execution_count": 14}], "source": ["X_train,Y_train=[],[]\n", "\n", "#20일 구간의 주가를 x 값으로하고, 그 다음날 값을 y 값으로 정리하기\n", "for i in range(20, n_train):\n", "  X_train.append(stock_values_scaled[i-20:i,0])\n", "  Y_train.append(stock_values_scaled[i,0])\n", "\n", "X_train[0] #작업 확인용 출력"]}, {"cell_type": "code", "source": ["Y_train[0] #작업 확인용 출력"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lMcAbFyn4t4e", "outputId": "190d80ab-9299-4b4a-8c3f-22b3c36a749c"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["np.float64(0.1177950505095311)"]}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "markdown", "metadata": {"id": "3hy2ZntepfmI"}, "source": ["### - LSTM 모델의 입력층의 구조로 변형_reshape()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "sHyz6S-ypfmJ", "outputId": "f4427f54-91b5-46d9-a034-e88f342436ad"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(699, 20, 1)\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["array([[0.02671203],\n", "       [0.03250785],\n", "       [0.02993439],\n", "       [0.05368445],\n", "       [0.06280789],\n", "       [0.04430047],\n", "       [0.03508124],\n", "       [0.03866933],\n", "       [0.03216959],\n", "       [0.03034587],\n", "       [0.05533445],\n", "       [0.0992329 ],\n", "       [0.10122109],\n", "       [0.10570054],\n", "       [0.10494639],\n", "       [0.11310075],\n", "       [0.07358582],\n", "       [0.08835883],\n", "       [0.07584842],\n", "       [0.10583761]])"]}, "metadata": {}, "execution_count": 16}], "source": ["X_train1, Y_train1 = np.array(X_train), np.array(Y_train)\n", "X_train2 = np.reshape(X_train1, (X_train1.shape[0],X_train1.shape[1],1))\n", "\n", "print(X_train2.shape) #작업 확인용 출력\n", "X_train2[0] #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "Bw95XPPupfmJ"}, "source": ["## 3-2. LSTM 모델 구축"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iNSB_hAApfmJ"}, "outputs": [], "source": ["from tensorflow.keras import Sequential\n", "from tensorflow.keras.layers import Dense, LSTM, Dropout"]}, {"cell_type": "markdown", "metadata": {"id": "wiWxIjqRpfmJ"}, "source": ["### - LSTM 모델 구성"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "d49nIq2Sg7Cv", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "f77c2369-1f21-432e-a97e-4a3570005318"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/layers/rnn/rnn.py:200: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(**kwargs)\n"]}], "source": ["lstm_stock = Sequential()\n", "\n", "lstm_stock.add(LSTM(units=20, return_sequences=True, input_shape=(X_train2.shape[1],1)))\n", "lstm_stock.add(LSTM(units=20, return_sequences=False))\n", "lstm_stock.add(Dense(1))"]}, {"cell_type": "markdown", "metadata": {"id": "bhoUdx0ipfmJ"}, "source": ["### - LSTM 모델 학습"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QbzEuUNGpfmJ", "outputId": "ab6121be-22b3-4025-f526-98d9569410a5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/10\n", "699/699 - 6s - 9ms/step - loss: 0.0051\n", "Epoch 2/10\n", "699/699 - 3s - 5ms/step - loss: 0.0019\n", "Epoch 3/10\n", "699/699 - 4s - 5ms/step - loss: 0.0014\n", "Epoch 4/10\n", "699/699 - 4s - 5ms/step - loss: 0.0011\n", "Epoch 5/10\n", "699/699 - 5s - 7ms/step - loss: 0.0010\n", "Epoch 6/10\n", "699/699 - 5s - 7ms/step - loss: 8.2713e-04\n", "Epoch 7/10\n", "699/699 - 5s - 7ms/step - loss: 7.1630e-04\n", "Epoch 8/10\n", "699/699 - 3s - 5ms/step - loss: 6.8331e-04\n", "Epoch 9/10\n", "699/699 - 6s - 8ms/step - loss: 6.8316e-04\n", "Epoch 10/10\n", "699/699 - 3s - 5ms/step - loss: 6.1991e-04\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["<keras.src.callbacks.history.History at 0x794a4c1f40d0>"]}, "metadata": {}, "execution_count": 19}], "source": ["#학습 파라미터  설정\n", "lstm_stock.compile(loss='mean_squared_error', optimizer='adam')\n", "\n", "#학습 수행\n", "lstm_stock.fit(X_train2, Y_train1, epochs=10, batch_size=1, verbose=2)"]}, {"cell_type": "markdown", "metadata": {"id": "fvzz8HBUpfmJ"}, "source": ["## 3-3. 평가 데이터로 주가 예측"]}, {"cell_type": "markdown", "metadata": {"id": "fKuFg6wkpfmJ"}, "source": ["### - 평가용 데이터 만들기"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KQjokJYeg_Js", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "5907ab9c-9b0f-4675-8bff-5fecf5f3e2fa"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([0.5036144 , 0.52107503, 0.52564579, 0.51001358, 0.50727109,\n", "       0.4867937 , 0.49739798, 0.51769261, 0.39473712, 0.36520959,\n", "       0.36484393, 0.38632677, 0.38221311, 0.40296464, 0.41219775,\n", "       0.4285612 , 0.43843431, 0.44711879, 0.45498073, 0.44062838])"]}, "metadata": {}, "execution_count": 20}], "source": ["stock_test = stock_values_scaled[n_train-20:]\n", "X_test = []\n", "\n", "for i in range(20, len(stock_test)):\n", "  X_test.append(stock_test[i-20:i,0])\n", "\n", "\n", "X_test[0] #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "ThB4TGJzpfmK"}, "source": ["### - LSTM 모델의 입력층의 구조로 변형_reshape()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6KOd5xT_pfmK", "outputId": "dd9d4dea-d39a-4018-9596-8a2f28bdf9a5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["(180, 20, 1)\n", "[[0.5036144 ]\n", " [0.52107503]\n", " [0.52564579]\n", " [0.51001358]\n", " [0.50727109]\n", " [0.4867937 ]\n", " [0.49739798]\n", " [0.51769261]\n", " [0.39473712]\n", " [0.36520959]\n", " [0.36484393]\n", " [0.38632677]\n", " [0.38221311]\n", " [0.40296464]\n", " [0.41219775]\n", " [0.4285612 ]\n", " [0.43843431]\n", " [0.44711879]\n", " [0.45498073]\n", " [0.44062838]]\n"]}], "source": ["X_test = np.array(X_test)\n", "X_test = np.reshape(X_test,(X_test.shape[0],X_test.shape[1],1))\n", "\n", "print(X_test.shape)  #작업 확인용 출력\n", "print(X_test[0])"]}, {"cell_type": "markdown", "metadata": {"id": "1zNSXUvYpfmK"}, "source": ["### - 모델 예측 수행"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FDBHybxOpfmK", "outputId": "20311b7f-a166-4c7c-945f-7d76917d309b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1m6/6\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 39ms/step\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["array([0.4439942], dtype=float32)"]}, "metadata": {}, "execution_count": 22}], "source": ["predicted_value = lstm_stock.predict(X_test)\n", "\n", "predicted_value[0] #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "xM6zoYMHpfmK"}, "source": ["# 4. 결과 분석 및 시각화"]}, {"cell_type": "markdown", "metadata": {"id": "6l_hM1mvpfmK"}, "source": ["##### - 예측값을 스케일링 이전 값(원래 주가)으로 되돌림."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "21jZENw_pfmP", "outputId": "7b9a8039-3ded-4ba3-f543-ad6e7eba99eb"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([131.43565], dtype=float32)"]}, "metadata": {}, "execution_count": 23}], "source": ["predicted_value = scaler.inverse_transform(predicted_value)\n", "\n", "predicted_value[0] #작업 확인용 출력"]}, {"cell_type": "markdown", "metadata": {"id": "lrayzUwEpfmP"}, "source": ["### - 그래프에 사용할 데이터 리스트 정리하기"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "epIZ1dorh5k7", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "69c6b65a-ca9a-4397-da50-a473d8005ccf"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-24-181e750172c1>:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  stock_test_vis['Predictions'] = predicted_value\n"]}], "source": ["stock_train_vis = stock[:n_train]\n", "stock_test_vis = stock[n_train:]\n", "stock_test_vis['Predictions'] = predicted_value"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HmNhapnapfmP"}, "outputs": [], "source": ["import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 652}, "id": "tybLU6rgpfmP", "outputId": "53e9fcdd-3849-49ae-fa1d-f945adb10a62"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Text(0.5, 1.0, 'GOOG (2021-01-01 ~ 2024-08-01 )')"]}, "metadata": {}, "execution_count": 26}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x800 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["plt.figure(figsize = (12,8))\n", "plt.plot(stock_train_vis['Close'], label = 'Train')\n", "plt.plot(stock_test_vis['Close'], label = 'Test')\n", "plt.plot(stock_test_vis['Predictions'], label = 'Prediction')\n", "plt.legend()\n", "plt.title(name + ' (' + start_day + ' ~ ' + end_day + ' )')"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}