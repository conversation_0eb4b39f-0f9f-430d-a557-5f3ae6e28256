"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/save_analysis_results";
exports.ids = ["pages/api/save_analysis_results"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_analysis_results&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_analysis_results.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_analysis_results&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_analysis_results.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_save_analysis_results_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\save_analysis_results.ts */ \"(api)/./src/pages/api/save_analysis_results.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_save_analysis_results_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_save_analysis_results_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/save_analysis_results\",\n        pathname: \"/api/save_analysis_results\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_save_analysis_results_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_analysis_results&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_analysis_results.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/data/sp500_enriched_final.ts":
/*!******************************************!*\
  !*** ./src/data/sp500_enriched_final.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QUICK_ENRICHED_FINAL: () => (/* binding */ QUICK_ENRICHED_FINAL)\n/* harmony export */ });\nconst QUICK_ENRICHED_FINAL = {\n    \"A\": {\n        name: \"Agilent Technologies\",\n        description: \"Provides life‑science and chemical analysis instruments, software and lab services.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"AAPL\": {\n        name: \"Apple Inc.\",\n        description: \"Designs iPhone, Mac and a growing ecosystem of consumer devices and digital services.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"ABBV\": {\n        name: \"AbbVie\",\n        description: \"Develops and markets specialty pharmaceuticals such as the immunology drug Humira.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"ABNB\": {\n        name: \"Airbnb\",\n        description: \"Runs a marketplace that matches travelers with short‑term home and experience rentals.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"ABT\": {\n        name: \"Abbott Laboratories\",\n        description: \"Supplies diagnostic equipment, medical devices and nutritional products worldwide.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"ACGL\": {\n        name: \"Arch Capital Group\",\n        description: \"Provides specialty insurance, reinsurance and mortgage insurance globally.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"ACN\": {\n        name: \"Accenture\",\n        description: \"Provides global consulting, technology outsourcing and digital transformation services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ADBE\": {\n        name: \"Adobe Inc.\",\n        description: \"Offers subscription software for creative design, digital documents and marketing analytics.\",\n        industry: \"Application Software\"\n    },\n    \"ADI\": {\n        name: \"Analog Devices\",\n        description: \"Supplies analog and mixed‑signal semiconductors used in sensing and power management.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"ADM\": {\n        name: \"Archer Daniels Midland\",\n        description: \"Processes crops into food ingredients, animal feed and biofuels.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"ADP\": {\n        name: \"Automatic Data Processing\",\n        description: \"Provides cloud‑based payroll, HR and workforce management solutions for employers.\",\n        industry: \"Application Software\"\n    },\n    \"ADSK\": {\n        name: \"Autodesk\",\n        description: \"Offers 3‑D design and engineering software such as AutoCAD and Revit via cloud subscriptions.\",\n        industry: \"Application Software\"\n    },\n    \"AEE\": {\n        name: \"Ameren\",\n        description: \"Generates and distributes electricity and natural gas in Missouri and Illinois.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AEP\": {\n        name: \"American Electric Power\",\n        description: \"Operates one of the largest transmission grids and generation fleets in the U.S.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AES\": {\n        name: \"AES Corporation\",\n        description: \"Generates and distributes electricity through a portfolio of global power plants.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"AFL\": {\n        name: \"Aflac\",\n        description: \"Sells supplemental health and accident insurance, primarily in the United States and Japan.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"AIG\": {\n        name: \"American International Group\",\n        description: \"Provides property‑casualty, life and retirement insurance solutions worldwide.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"AIZ\": {\n        name: \"Assurant\",\n        description: \"Provides specialty insurance covering mobile devices, extended warranties and lender services.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"AJG\": {\n        name: \"Arthur J. Gallagher & Co.\",\n        description: \"Offers insurance brokerage and risk management services to businesses worldwide.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"AKAM\": {\n        name: \"Akamai Technologies\",\n        description: \"Delivers content‑delivery and cloud security services that speed and protect web traffic.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ALB\": {\n        name: \"Albemarle Corporation\",\n        description: \"Produces lithium, bromine and other specialty chemicals for batteries and industrial uses.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"ALGN\": {\n        name: \"Align Technology\",\n        description: \"Manufactures the Invisalign clear dental aligner system and 3‑D scanners.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"ALL\": {\n        name: \"Allstate\",\n        description: \"Offers auto, homeowners and other personal property‑casualty insurance.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"ALLE\": {\n        name: \"Allegion\",\n        description: \"Makes commercial and residential locks, door hardware and access‑control systems.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"AMAT\": {\n        name: \"Applied Materials\",\n        description: \"Supplies semiconductor fabrication equipment and process materials to chipmakers.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"AMCR\": {\n        name: \"Amcor\",\n        description: \"Produces flexible and rigid packaging for food, beverage and healthcare products.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"AMD\": {\n        name: \"Advanced Micro Devices\",\n        description: \"Designs CPUs and GPUs for PCs, gaming consoles and data‑center servers, outsourcing fabrication.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"AME\": {\n        name: \"Ametek\",\n        description: \"Produces electronic instruments and electromechanical components for industrial markets.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"AMGN\": {\n        name: \"Amgen\",\n        description: \"Researches and manufactures biologic therapies for oncology, inflammation and rare diseases.\",\n        industry: \"Biotechnology\"\n    },\n    \"AMP\": {\n        name: \"Ameriprise Financial\",\n        description: \"Offers wealth management, asset management and annuity products.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"AMT\": {\n        name: \"American Tower\",\n        description: \"Leases wireless towers and data centers to mobile network operators across the globe.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"AMZN\": {\n        name: \"Amazon\",\n        description: \"Runs the world’s largest e‑commerce marketplace and the AWS cloud infrastructure business.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"ANET\": {\n        name: \"Arista Networks\",\n        description: \"Develops high‑performance Ethernet switches and cloud networking software.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"ANSS\": {\n        name: \"Ansys\",\n        description: \"Provides engineering simulation software used for product design and testing.\",\n        industry: \"Application Software\"\n    },\n    \"AON\": {\n        name: \"Aon plc\",\n        description: \"Delivers insurance brokerage, reinsurance and risk advisory services.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"AOS\": {\n        name: \"A. O. Smith\",\n        description: \"Designs and manufactures residential and commercial water heaters and boilers.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"APA\": {\n        name: \"APA Corporation\",\n        description: \"Explores for and produces oil and natural gas resources in the U.S. and overseas.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"APD\": {\n        name: \"Air Products\",\n        description: \"Supplies industrial and specialty gases such as hydrogen, nitrogen and oxygen.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"APH\": {\n        name: \"Amphenol\",\n        description: \"Designs and manufactures electronic connectors, cables and antenna solutions.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"APO\": {\n        name: \"Apollo Global Management\",\n        description: \"Manages private equity, credit and real‑estate funds for institutional investors.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"APTV\": {\n        name: \"Aptiv\",\n        description: \"Produces advanced electrical architectures and software for automotive safety and connectivity.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"ARE\": {\n        name: \"Alexandria Real Estate Equities\",\n        description: \"Owns and leases life‑science laboratory campuses to biotech and pharma tenants.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"ATO\": {\n        name: \"Atmos Energy\",\n        description: \"Delivers natural gas utility service to customers in the southern United States.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AVB\": {\n        name: \"AvalonBay Communities\",\n        description: \"Develops and manages multifamily apartment communities in high‑growth U.S. markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"AVGO\": {\n        name: \"Broadcom\",\n        description: \"Designs high‑performance semiconductor chips for networking, wireless and storage.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"AVY\": {\n        name: \"Avery Dennison\",\n        description: \"Produces pressure‑sensitive labels, RFID tags and packaging materials.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"AWK\": {\n        name: \"American Water Works\",\n        description: \"Owns regulated water and wastewater utilities serving customers in multiple states.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"AXON\": {\n        name: \"Axon Enterprise\",\n        description: \"Manufactures TASER devices, body cameras and digital evidence management software for public safety.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"AXP\": {\n        name: \"American Express\",\n        description: \"Issues premium charge and credit cards and operates a global payments network.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"AZO\": {\n        name: \"AutoZone\",\n        description: \"Operates a chain of retail stores supplying replacement auto parts and accessories.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"BA\": {\n        name: \"Boeing\",\n        description: \"Manufactures commercial jetliners, defense aircraft and space systems.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"BAC\": {\n        name: \"Bank of America\",\n        description: \"Offers retail and investment banking, credit cards and wealth management services.\",\n        industry: \"Banks\"\n    },\n    \"BALL\": {\n        name: \"Ball Corporation\",\n        description: \"Makes recyclable aluminum beverage cans and aerospace components.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"BAX\": {\n        name: \"Baxter International\",\n        description: \"Supplies hospital products and medical devices for critical care and dialysis.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"BBY\": {\n        name: \"Best Buy\",\n        description: \"Runs a nationwide chain of big‑box stores selling consumer electronics and appliances.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"BDX\": {\n        name: \"Becton Dickinson\",\n        description: \"Manufactures needles, syringes and diagnostic instruments used in hospitals and labs.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"BEN\": {\n        name: \"Franklin Resources\",\n        description: \"Manages mutual funds and institutional assets under the Franklin Templeton brand.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"BG\": {\n        name: \"Bunge Global\",\n        description: \"Processes oilseeds and grains into food ingredients, animal feed and biofuels.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"BIIB\": {\n        name: \"Biogen\",\n        description: \"Develops therapies for neurological diseases such as multiple sclerosis and ALS.\",\n        industry: \"Biotechnology\"\n    },\n    \"BK\": {\n        name: \"BNY Mellon\",\n        description: \"Provides custodial banking, clearing and asset‑servicing for institutional investors.\",\n        industry: \"Banks\"\n    },\n    \"BKNG\": {\n        name: \"Booking Holdings\",\n        description: \"Operates Booking.com, Priceline and other platforms that reserve lodging and travel services.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"BKR\": {\n        name: \"Baker Hughes\",\n        description: \"Provides oilfield services, equipment and technology to the energy industry.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"BLDR\": {\n        name: \"Builders FirstSource\",\n        description: \"Distributes lumber and prefabricated components to residential homebuilders.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"BLK\": {\n        name: \"BlackRock\",\n        description: \"Manages global index and active funds, including the iShares ETF family.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"BMY\": {\n        name: \"Bristol Myers Squibb\",\n        description: \"Researches and markets prescription drugs for oncology, immunology and cardiovascular care.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"BR\": {\n        name: \"Broadridge Financial Solutions\",\n        description: \"Provides investor communications, proxy processing and fintech back‑office platforms.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"BRO\": {\n        name: \"Brown & Brown\",\n        description: \"Brokers property‑and‑casualty insurance and employee benefits for businesses.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"BSX\": {\n        name: \"Boston Scientific\",\n        description: \"Makes minimally invasive medical devices like cardiac stents and catheters.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"BX\": {\n        name: \"Blackstone Inc.\",\n        description: \"Operates private‑equity, credit, real‑estate and hedge‑fund investment vehicles.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"BXP\": {\n        name: \"BXP, Inc.\",\n        description: \"Owns and manages Class‑A office buildings in major U.S. gateway cities.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"C\": {\n        name: \"Citigroup\",\n        description: \"Provides global consumer banking, credit cards and institutional financial services.\",\n        industry: \"Banks\"\n    },\n    \"CAG\": {\n        name: \"Conagra Brands\",\n        description: \"Produces frozen foods, snacks and condiments under brands like Healthy Choice and Slim Jim.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CAH\": {\n        name: \"Cardinal Health\",\n        description: \"Distributes pharmaceuticals and medical‑surgical supplies to hospitals and pharmacies.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CARR\": {\n        name: \"Carrier Global\",\n        description: \"Manufactures HVAC, refrigeration and fire‑safety equipment for buildings.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"CAT\": {\n        name: \"Caterpillar\",\n        description: \"Produces heavy construction, mining and energy equipment under the Caterpillar brand.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"CB\": {\n        name: \"Chubb Limited\",\n        description: \"Provides global property, casualty and reinsurance coverage to commercial and personal clients.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"CBOE\": {\n        name: \"Cboe Global Markets\",\n        description: \"Operates options, equities and futures exchanges along with market‑data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"CBRE\": {\n        name: \"CBRE Group\",\n        description: \"Provides commercial real‑estate brokerage, investment management and facilities services.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"CCI\": {\n        name: \"Crown Castle\",\n        description: \"Owns and leases wireless towers, small cells and fiber for mobile carriers across the U.S.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"CCL\": {\n        name: \"Carnival\",\n        description: \"Runs Carnival, Princess and other cruise lines offering global vacation voyages.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"CDNS\": {\n        name: \"Cadence Design Systems\",\n        description: \"Offers electronic design automation software used to design complex semiconductors.\",\n        industry: \"Application Software\"\n    },\n    \"CDW\": {\n        name: \"CDW Corporation\",\n        description: \"Resells IT hardware, software and cloud solutions to businesses and governments.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"CEG\": {\n        name: \"Constellation Energy\",\n        description: \"Generates electricity primarily from nuclear and renewable assets and sells it wholesale.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"CF\": {\n        name: \"CF Industries\",\n        description: \"Produces nitrogen‑based fertilizers such as ammonia, urea and UAN for agriculture.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"CFG\": {\n        name: \"Citizens Financial Group\",\n        description: \"Operates regional retail and commercial banking centered in New England and the Midwest.\",\n        industry: \"Banks\"\n    },\n    \"CHD\": {\n        name: \"Church & Dwight\",\n        description: \"Markets household and personal‑care brands including Arm & Hammer and Trojan.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CHRW\": {\n        name: \"C.H. Robinson\",\n        description: \"Arranges freight shipments and supply‑chain services as a third‑party logistics provider.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"CHTR\": {\n        name: \"Charter Communications\",\n        description: \"Supplies cable television, broadband internet and voice services under the Spectrum brand.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"CI\": {\n        name: \"Cigna\",\n        description: \"Offers medical, dental and pharmacy benefit plans and runs a large PBM unit.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CINF\": {\n        name: \"Cincinnati Financial\",\n        description: \"Underwrites commercial and personal property‑and‑casualty insurance through independent agents.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"CL\": {\n        name: \"Colgate-Palmolive\",\n        description: \"Sells Colgate toothpaste, Palmolive soaps and other oral and personal‑care products.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CLX\": {\n        name: \"Clorox\",\n        description: \"Produces bleach, disinfecting wipes and household cleaning products under Clorox and other brands.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CMCSA\": {\n        name: \"Comcast\",\n        description: \"Provides broadband, cable TV and owns NBCUniversal’s media and theme‑park assets.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"CME\": {\n        name: \"CME Group\",\n        description: \"Runs futures and options exchanges trading commodities, rates and equities worldwide.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"CMG\": {\n        name: \"Chipotle Mexican Grill\",\n        description: \"Operates fast‑casual restaurants serving made‑to‑order burritos and bowls.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"CMI\": {\n        name: \"Cummins\",\n        description: \"Manufactures diesel and alternative‑fuel engines, powertrains and generators for trucks and equipment.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"CMS\": {\n        name: \"CMS Energy\",\n        description: \"Generates and distributes electricity and gas to customers across Michigan.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"CNC\": {\n        name: \"Centene\",\n        description: \"Offers managed‑care health plans focused on Medicaid and government programs.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CNP\": {\n        name: \"CenterPoint Energy\",\n        description: \"Delivers regulated electric and natural‑gas service in Texas and neighboring states.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"COF\": {\n        name: \"Capital One Financial\",\n        description: \"Issues credit cards and provides retail and commercial banking services in the U.S.\",\n        industry: \"Banks\"\n    },\n    \"COIN\": {\n        name: \"Coinbase\",\n        description: \"Operates a regulated cryptocurrency exchange, custody and blockchain services platform.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"COO\": {\n        name: \"Cooper Companies (The)\",\n        description: \"Produces contact lenses and surgical devices for women's health under CooperVision and CooperSurgical.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"COP\": {\n        name: \"ConocoPhillips\",\n        description: \"Explores and produces crude oil and natural gas across global basins.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"COR\": {\n        name: \"Cencora\",\n        description: \"Provides drug‑distribution and services to pharmaceutical manufacturers and providers.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"COST\": {\n        name: \"Costco\",\n        description: \"Operates membership‑only warehouse clubs selling bulk groceries, appliances and general merchandise.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"CPAY\": {\n        name: \"Corpay\",\n        description: \"Provides cross‑border B2B payment and expense management solutions to enterprises.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"CPB\": {\n        name: \"Campbell Soup Company\",\n        description: \"Produces branded soups, snacks and meals under Campbell's, Pepperidge Farm and V8.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"CPRT\": {\n        name: \"Copart\",\n        description: \"Runs online auctions for salvage and used vehicles, handling storage, logistics and title transfer.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"CPT\": {\n        name: \"Camden Property Trust\",\n        description: \"Develops, owns and leases multifamily apartment communities in high‑growth markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"CRL\": {\n        name: \"Charles River Laboratories\",\n        description: \"Runs preclinical CRO labs that test the safety and efficacy of new drug candidates.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CRM\": {\n        name: \"Salesforce\",\n        description: \"Delivers cloud customer relationship management software under the Salesforce platform.\",\n        industry: \"Application Software\"\n    },\n    \"CRWD\": {\n        name: \"CrowdStrike\",\n        description: \"Offers Falcon endpoint security platform for real‑time threat detection and response.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"CSCO\": {\n        name: \"Cisco Systems\",\n        description: \"Builds networking routers, switches and security hardware plus collaboration software.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"CSGP\": {\n        name: \"CoStar Group\",\n        description: \"Supplies commercial real‑estate listings, analytics and marketplaces like LoopNet and Apartments.com.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"CSX\": {\n        name: \"CSX Corporation\",\n        description: \"Operates a major U.S. freight railroad network serving the eastern United States.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"CTAS\": {\n        name: \"Cintas\",\n        description: \"Rents uniforms, mats and facility supplies to businesses across North America.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"CTRA\": {\n        name: \"Coterra\",\n        description: \"Explores for and produces oil and natural gas in the Permian, Marcellus and Anadarko basins.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"CTSH\": {\n        name: \"Cognizant Technology Solutions\",\n        description: \"Delivers IT consulting, digital engineering and business‑process outsourcing services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"CTVA\": {\n        name: \"Corteva\",\n        description: \"Develops crop‑protection chemicals and high‑yield seeds for global agriculture markets.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"CVS\": {\n        name: \"CVS Health\",\n        description: \"Runs CVS pharmacies, MinuteClinics and a large pharmacy‑benefit and health‑insurance business.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"CVX\": {\n        name: \"Chevron\",\n        description: \"Explores, produces and refines oil and natural gas on a global scale.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"CZR\": {\n        name: \"Caesars Entertainment\",\n        description: \"Operates casino resorts and digital sports‑betting platforms across North America.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"D\": {\n        name: \"Dominion Energy\",\n        description: \"Generates and distributes regulated electricity and natural gas in the Mid‑Atlantic region.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"DAL\": {\n        name: \"Delta Air Lines\",\n        description: \"Provides global passenger and cargo air transportation with a main hub in Atlanta.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"DASH\": {\n        name: \"DoorDash\",\n        description: \"Operates an on‑demand logistics platform delivering restaurant meals and retail goods.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"DAY\": {\n        name: \"Dayforce\",\n        description: \"Sells Dayforce cloud software for human‑capital management and payroll processing.\",\n        industry: \"Application Software\"\n    },\n    \"DD\": {\n        name: \"DuPont\",\n        description: \"Produces specialty materials, resins and electronics materials for industrial applications.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"DE\": {\n        name: \"Deere & Company\",\n        description: \"Builds John Deere tractors, combines and precision agriculture machinery.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"DECK\": {\n        name: \"Deckers Brands\",\n        description: \"Designs and markets footwear brands including UGG, HOKA ONE ONE and Teva.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"DELL\": {\n        name: \"Dell Technologies\",\n        description: \"Sells personal computers, servers, storage and IT services to consumers and enterprises.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"DG\": {\n        name: \"Dollar General\",\n        description: \"Runs Dollar General discount stores offering low‑priced everyday household items.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"DGX\": {\n        name: \"Quest Diagnostics\",\n        description: \"Operates a network of medical laboratories providing diagnostic testing services.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"DHI\": {\n        name: \"D. R. Horton\",\n        description: \"Constructs and sells single‑family homes and residential communities across the U.S.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"DHR\": {\n        name: \"Danaher Corporation\",\n        description: \"Supplies life‑science instruments, diagnostics and water‑quality equipment through a portfolio of subsidiaries.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"DIS\": {\n        name: \"Walt Disney Company (The)\",\n        description: \"Produces films, streaming content, theme parks and consumer products.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"DLR\": {\n        name: \"Digital Realty\",\n        description: \"Owns and leases carrier‑neutral data centers supporting cloud and enterprise workloads.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"DLTR\": {\n        name: \"Dollar Tree\",\n        description: \"Operates Dollar Tree and Family Dollar variety stores offering discounted merchandise.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"DOC\": {\n        name: \"Healthpeak Properties\",\n        description: \"Owns medical office buildings and life‑science campuses as a healthcare REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"DOV\": {\n        name: \"Dover Corporation\",\n        description: \"Manufactures industrial components such as pumps, compressors and marking systems.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"DOW\": {\n        name: \"Dow Inc.\",\n        description: \"Produces basic plastics, chemicals and intermediates used in packaging and construction.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"DPZ\": {\n        name: \"Domino's\",\n        description: \"Franchises Domino’s pizza restaurants offering delivery and carry‑out service worldwide.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"DRI\": {\n        name: \"Darden Restaurants\",\n        description: \"Operates full‑service restaurant chains including Olive Garden and LongHorn Steakhouse.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"DTE\": {\n        name: \"DTE Energy\",\n        description: \"Generates electricity and distributes gas and power to customers in Michigan.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"DUK\": {\n        name: \"Duke Energy\",\n        description: \"Provides electric and gas utility service across the Carolinas, Florida and Midwest.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"DVA\": {\n        name: \"DaVita\",\n        description: \"Operates outpatient dialysis centers treating patients with chronic kidney failure.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"DVN\": {\n        name: \"Devon Energy\",\n        description: \"Explores and produces oil and gas with a focus on the Delaware and Anadarko basins.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"DXCM\": {\n        name: \"Dexcom\",\n        description: \"Develops continuous glucose monitoring systems for diabetes management.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"EA\": {\n        name: \"Electronic Arts\",\n        description: \"Creates and publishes video game franchises such as FIFA, Madden NFL and The Sims.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"EBAY\": {\n        name: \"eBay Inc.\",\n        description: \"Runs an online marketplace connecting sellers and buyers of new and used goods.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"ECL\": {\n        name: \"Ecolab\",\n        description: \"Provides water, hygiene and infection‑prevention chemicals and services for hospitality and industry.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"ED\": {\n        name: \"Consolidated Edison\",\n        description: \"Supplies electric and steam service to New York City and gas to surrounding areas.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"EFX\": {\n        name: \"Equifax\",\n        description: \"Maintains consumer credit databases and offers identity‑verification analytics.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"EG\": {\n        name: \"Everest Group\",\n        description: \"Offers specialty reinsurance and insurance products to global clients.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"EIX\": {\n        name: \"Edison International\",\n        description: \"Supplies electricity to Southern California through its regulated utility SCE.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"EL\": {\n        name: \"Estée Lauder Companies (The)\",\n        description: \"Markets prestige skincare, makeup and fragrance brands including Estée Lauder and MAC.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"ELV\": {\n        name: \"Elevance Health\",\n        description: \"Provides Blue Cross Blue Shield‑branded health insurance plans across the U.S.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"EMN\": {\n        name: \"Eastman Chemical Company\",\n        description: \"Makes specialty plastics, additives and fibers derived from acetyl and polyester chemistry.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"EMR\": {\n        name: \"Emerson Electric\",\n        description: \"Provides industrial automation systems, valves and measurement equipment.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"ENPH\": {\n        name: \"Enphase Energy\",\n        description: \"Designs microinverters and battery storage solutions for residential solar energy systems.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"EOG\": {\n        name: \"EOG Resources\",\n        description: \"Produces crude oil and natural gas with a focus on shale plays in the U.S.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"EPAM\": {\n        name: \"EPAM Systems\",\n        description: \"Offers outsourced software engineering and digital product design services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"EQIX\": {\n        name: \"Equinix\",\n        description: \"Operates global colocation data centers interconnecting cloud and network providers.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"EQR\": {\n        name: \"Equity Residential\",\n        description: \"Owns and manages high‑end apartment communities in urban U.S. markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"EQT\": {\n        name: \"EQT Corporation\",\n        description: \"Produces natural gas primarily from the Marcellus and Utica shales.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"ERIE\": {\n        name: \"Erie Indemnity\",\n        description: \"Provides auto, home and business insurance through independent agents.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"ES\": {\n        name: \"Eversource Energy\",\n        description: \"Delivers regulated electric and natural‑gas service in New England.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"ESS\": {\n        name: \"Essex Property Trust\",\n        description: \"Owns Class‑A multifamily properties on the U.S. West Coast.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"ETN\": {\n        name: \"Eaton Corporation\",\n        description: \"Supplies electrical power‑management equipment, hydraulics and aerospace components.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"ETR\": {\n        name: \"Entergy\",\n        description: \"Generates nuclear and fossil power and distributes electricity in the Gulf South.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"EXE\": {\n        name: \"Expand Energy\",\n        description: \"Information unavailable.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"EXPD\": {\n        name: \"Expeditors International\",\n        description: \"Provides freight forwarding and customs brokerage services across air, ocean and ground routes.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"EXPE\": {\n        name: \"Expedia Group\",\n        description: \"Operates global online travel agencies including Expedia, Hotels.com and Vrbo.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"EXR\": {\n        name: \"Extra Space Storage\",\n        description: \"Owns and manages self‑storage facilities across the United States.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"F\": {\n        name: \"Ford Motor Company\",\n        description: \"Designs, manufactures and sells Ford and Lincoln vehicles and related services worldwide.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"FAST\": {\n        name: \"Fastenal\",\n        description: \"Distributes industrial and construction fasteners, tools and safety supplies.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"FCX\": {\n        name: \"Freeport-McMoRan\",\n        description: \"Mines copper, gold and molybdenum with major operations in the Americas and Indonesia.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"FDS\": {\n        name: \"FactSet\",\n        description: \"Delivers financial data, analytics and workflow tools to investment professionals.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"FDX\": {\n        name: \"FedEx\",\n        description: \"Provides global express parcel delivery, ground shipping and logistics solutions.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"FE\": {\n        name: \"FirstEnergy\",\n        description: \"Generates and distributes regulated electricity to customers in the U.S. Midwest and Mid‑Atlantic.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"FFIV\": {\n        name: \"F5, Inc.\",\n        description: \"Supplies application‑delivery and security platforms that optimize and protect network traffic.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"FI\": {\n        name: \"Fiserv\",\n        description: \"Provides payment processing, core banking and merchant acquiring technology.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"FICO\": {\n        name: \"Fair Isaac\",\n        description: \"Creates FICO credit scores and decision‑analytics software for lenders.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"FIS\": {\n        name: \"Fidelity National Information Services\",\n        description: \"Supplies core banking, card processing and treasury software to financial institutions.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"FITB\": {\n        name: \"Fifth Third Bancorp\",\n        description: \"Operates a regional banking network offering loans, deposits and payment services.\",\n        industry: \"Banks\"\n    },\n    \"FOX\": {\n        name: \"Fox Corporation (Class B)\",\n        description: \"Operates Fox News, Fox Sports and broadcast television stations in the United States.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"FOXA\": {\n        name: \"Fox Corporation (Class A)\",\n        description: \"Operates Fox News, Fox Sports and broadcast television stations in the United States.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"FRT\": {\n        name: \"Federal Realty Investment Trust\",\n        description: \"Owns and redevelops grocery‑anchored shopping centers in major U.S. metros.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"FSLR\": {\n        name: \"First Solar\",\n        description: \"Manufactures thin‑film photovoltaic modules and develops utility‑scale solar projects.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"FTNT\": {\n        name: \"Fortinet\",\n        description: \"Develops network firewalls and security operating systems for enterprise cyber defense.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"FTV\": {\n        name: \"Fortive\",\n        description: \"Produces industrial measurement, sensing and automation equipment through diversified brands.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"GD\": {\n        name: \"General Dynamics\",\n        description: \"Builds military vehicles, submarines, IT systems and Gulfstream business jets.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"GDDY\": {\n        name: \"GoDaddy\",\n        description: \"Provides domain registration, web hosting and small‑business cloud services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"GE\": {\n        name: \"GE Aerospace\",\n        description: \"Manufactures jet and turboprop engines for commercial and military aircraft.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"GEHC\": {\n        name: \"GE HealthCare\",\n        description: \"Supplies medical imaging, ultrasound and patient monitoring equipment worldwide.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"GEN\": {\n        name: \"Gen Digital\",\n        description: \"Offers Norton and LifeLock cybersecurity software for consumers and small businesses.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"GEV\": {\n        name: \"GE Vernova\",\n        description: \"Produces wind turbines, grid solutions and power‑generation services focused on clean energy.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"GILD\": {\n        name: \"Gilead Sciences\",\n        description: \"Develops antiviral and oncology therapeutics including HIV and hepatitis C treatments.\",\n        industry: \"Biotechnology\"\n    },\n    \"GIS\": {\n        name: \"General Mills\",\n        description: \"Produces branded cereals, snacks and refrigerated meals such as Cheerios and Yoplait.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"GL\": {\n        name: \"Globe Life\",\n        description: \"Offers life and supplemental health insurance targeting middle‑income households.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"GM\": {\n        name: \"General Motors\",\n        description: \"Designs and manufactures Chevrolet, GMC, Cadillac and Buick vehicles worldwide.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"GNRC\": {\n        name: \"Generac\",\n        description: \"Manufactures standby generators and energy storage systems for residential and commercial use.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"GOOG\": {\n        name: \"Alphabet Inc. (Class C)\",\n        description: \"Operates Google search, YouTube, Android and a growing cloud computing platform.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"GOOGL\": {\n        name: \"Alphabet Inc. (Class A)\",\n        description: \"Operates Google search, YouTube, Android and a growing cloud computing platform.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"GPC\": {\n        name: \"Genuine Parts Company\",\n        description: \"Distributes automotive replacement parts through the NAPA brand and industrial components.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"GPN\": {\n        name: \"Global Payments\",\n        description: \"Provides merchant acquiring, card issuing and point‑of‑sale payment solutions.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"GRMN\": {\n        name: \"Garmin\",\n        description: \"Designs GPS navigation devices, fitness wearables and avionics systems.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"GS\": {\n        name: \"Goldman Sachs\",\n        description: \"Delivers investment banking, trading, asset management and consumer banking services.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"GWW\": {\n        name: \"W. W. Grainger\",\n        description: \"Distributes maintenance, repair and safety supplies to industrial customers.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"HAL\": {\n        name: \"Halliburton\",\n        description: \"Provides drilling, completions and production services to the oil and gas industry.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"HAS\": {\n        name: \"Hasbro\",\n        description: \"Designs and markets toys, games and entertainment content such as Monopoly and Transformers.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"HBAN\": {\n        name: \"Huntington Bancshares\",\n        description: \"Operates community banking branches offering consumer and commercial financial services.\",\n        industry: \"Banks\"\n    },\n    \"HCA\": {\n        name: \"HCA Healthcare\",\n        description: \"Operates hospitals and outpatient surgery centers across the United States.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"HD\": {\n        name: \"Home Depot (The)\",\n        description: \"Operates big‑box stores selling home‑improvement products and building materials.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"HES\": {\n        name: \"Hess Corporation\",\n        description: \"Explores and produces crude oil and natural gas in North Dakota, Guyana and other regions.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"HIG\": {\n        name: \"Hartford (The)\",\n        description: \"Offers property‑casualty, group benefits and mutual fund products.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"HII\": {\n        name: \"Huntington Ingalls Industries\",\n        description: \"Designs and builds nuclear‑powered aircraft carriers and submarines for the U.S. Navy.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"HLT\": {\n        name: \"Hilton Worldwide\",\n        description: \"Franchises and manages hotel brands including Hilton, Waldorf Astoria and Hampton.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"HOLX\": {\n        name: \"Hologic\",\n        description: \"Develops diagnostic imaging and testing products focused on women's health.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"HON\": {\n        name: \"Honeywell\",\n        description: \"Produces aerospace systems, industrial automation and building technologies as a diversified manufacturer.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"HPE\": {\n        name: \"Hewlett Packard Enterprise\",\n        description: \"Provides servers, storage and edge‑to‑cloud IT solutions for enterprises.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"HPQ\": {\n        name: \"HP Inc.\",\n        description: \"Produces personal computers, printers and related supplies for consumers and businesses.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"HRL\": {\n        name: \"Hormel Foods\",\n        description: \"Processes and markets branded meat and food products such as Spam and Skippy.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"HSIC\": {\n        name: \"Henry Schein\",\n        description: \"Distributes dental and medical supplies along with practice‑management software.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"HST\": {\n        name: \"Host Hotels & Resorts\",\n        description: \"Owns upscale hotel properties operated under leading hospitality brands.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"HSY\": {\n        name: \"Hershey Company (The)\",\n        description: \"Produces chocolate and confectionery brands such as Hershey's, Reese's and KitKat.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"HUBB\": {\n        name: \"Hubbell Incorporated\",\n        description: \"Manufactures electrical wiring, lighting and utility infrastructure products.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"HUM\": {\n        name: \"Humana\",\n        description: \"Provides Medicare Advantage and other health insurance plans with integrated care services.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"HWM\": {\n        name: \"Howmet Aerospace\",\n        description: \"Supplies engineered forged and cast metal components for aircraft engines and structures.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"IBM\": {\n        name: \"IBM\",\n        description: \"Delivers hybrid‑cloud platforms, AI software and enterprise mainframes for global businesses.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ICE\": {\n        name: \"Intercontinental Exchange\",\n        description: \"Operates global commodity and equity exchanges and provides market data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"IDXX\": {\n        name: \"Idexx Laboratories\",\n        description: \"Provides veterinary diagnostic tests, imaging and practice‑management software.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"IEX\": {\n        name: \"IDEX Corporation\",\n        description: \"Manufactures specialized fluid‑handling pumps, meters and fire‑rescue equipment.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"IFF\": {\n        name: \"International Flavors & Fragrances\",\n        description: \"Creates flavors, fragrances and cosmetic ingredients for food and consumer products.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"INCY\": {\n        name: \"Incyte\",\n        description: \"Develops small‑molecule and antibody therapies for cancer and inflammatory diseases.\",\n        industry: \"Biotechnology\"\n    },\n    \"INTC\": {\n        name: \"Intel\",\n        description: \"Designs and manufactures x86 processors, chipsets and data‑center semiconductors.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"INTU\": {\n        name: \"Intuit\",\n        description: \"Offers TurboTax, QuickBooks and cloud financial software for consumers and small businesses.\",\n        industry: \"Application Software\"\n    },\n    \"INVH\": {\n        name: \"Invitation Homes\",\n        description: \"Owns and leases single‑family rental homes across U.S. sunbelt markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"IP\": {\n        name: \"International Paper\",\n        description: \"Produces containerboard, corrugated packaging and pulp products.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"IPG\": {\n        name: \"Interpublic Group of Companies (The)\",\n        description: \"Runs global advertising, public‑relations and marketing agencies under multiple networks.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"IQV\": {\n        name: \"IQVIA\",\n        description: \"Provides contract research, real‑world data and analytics for the life‑sciences industry.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"IR\": {\n        name: \"Ingersoll Rand\",\n        description: \"Supplies compressed‑air systems, pumps and vacuum solutions for industrial applications.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"IRM\": {\n        name: \"Iron Mountain\",\n        description: \"Offers records storage, secure shredding and data‑center colocation services.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ISRG\": {\n        name: \"Intuitive Surgical\",\n        description: \"Develops and sells the da Vinci robotic surgical systems and instruments.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"IT\": {\n        name: \"Gartner\",\n        description: \"Provides IT research, consulting and conferences for executives and technology vendors.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"ITW\": {\n        name: \"Illinois Tool Works\",\n        description: \"Produces engineered fasteners, welding equipment and food‑service machinery.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"IVZ\": {\n        name: \"Invesco\",\n        description: \"Manages mutual funds and ETFs, including the Invesco QQQ Trust.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"J\": {\n        name: \"Jacobs Solutions\",\n        description: \"Delivers engineering, construction and technical consulting for infrastructure and defense projects.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"JBHT\": {\n        name: \"J.B. Hunt\",\n        description: \"Provides trucking, intermodal and last‑mile freight logistics across North America.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"JBL\": {\n        name: \"Jabil\",\n        description: \"Delivers electronics manufacturing services and supply‑chain solutions for global brands.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"JCI\": {\n        name: \"Johnson Controls\",\n        description: \"Provides HVAC equipment, building automation and fire‑security systems for commercial facilities.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"JKHY\": {\n        name: \"Jack Henry & Associates\",\n        description: \"Supplies core banking and payments software to community financial institutions.\",\n        industry: \"Application Software\"\n    },\n    \"JNJ\": {\n        name: \"Johnson & Johnson\",\n        description: \"Researches, manufactures and sells pharmaceuticals, medical devices and consumer health products.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"JNPR\": {\n        name: \"Juniper Networks\",\n        description: \"Designs networking switches, routers and security software for enterprise and telecom markets.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"JPM\": {\n        name: \"JPMorgan Chase\",\n        description: \"Offers global consumer banking, investment banking, asset management and payments services.\",\n        industry: \"Banks\"\n    },\n    \"K\": {\n        name: \"Kellanova\",\n        description: \"Produces branded snacks and cereal products including Pringles and Cheez‑It.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KDP\": {\n        name: \"Keurig Dr Pepper\",\n        description: \"Brews and distributes Keurig coffee pods and Dr Pepper soft drinks.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"KEY\": {\n        name: \"KeyCorp\",\n        description: \"Operates a regional banking network offering deposits, loans and wealth services.\",\n        industry: \"Banks\"\n    },\n    \"KEYS\": {\n        name: \"Keysight Technologies\",\n        description: \"Provides electronic test instruments and simulation software for 5G and aerospace markets.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"KHC\": {\n        name: \"Kraft Heinz\",\n        description: \"Produces branded condiments, cheese and packaged meals under Kraft and Heinz.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KIM\": {\n        name: \"Kimco Realty\",\n        description: \"Owns grocery‑anchored open‑air shopping centers across the United States.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"KKR\": {\n        name: \"KKR & Co.\",\n        description: \"Manages private‑equity, credit and infrastructure investment funds for institutions.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"KLAC\": {\n        name: \"KLA Corporation\",\n        description: \"Supplies process‑control and yield‑management equipment for advanced semiconductor fabs.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"KMB\": {\n        name: \"Kimberly‑Clark\",\n        description: \"Makes personal‑care tissue products such as Huggies diapers and Kleenex tissues.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KMI\": {\n        name: \"Kinder Morgan\",\n        description: \"Operates oil and natural‑gas pipelines, storage and terminal assets across North America.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"KMX\": {\n        name: \"CarMax\",\n        description: \"Buys, refurbishes and sells used cars through large retail superstores and online.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"KO\": {\n        name: \"Coca-Cola Company\",\n        description: \"Manufactures and distributes Coca‑Cola beverages and a portfolio of soft‑drink brands.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"KR\": {\n        name: \"Kroger\",\n        description: \"Runs a nationwide chain of supermarkets and fuel centers with private‑label brands.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"KVUE\": {\n        name: \"Kenvue\",\n        description: \"Markets over‑the‑counter health and personal‑care brands such as Tylenol and Listerine.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"L\": {\n        name: \"Loews Corporation\",\n        description: \"Holds diversified interests in insurance, energy and lodging through subsidiary companies.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"LDOS\": {\n        name: \"Leidos\",\n        description: \"Provides IT services, intelligence analysis and engineering solutions for defense and civil agencies.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"LEN\": {\n        name: \"Lennar\",\n        description: \"Builds and sells single‑family homes and multifamily residences in the United States.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"LH\": {\n        name: \"Labcorp\",\n        description: \"Operates clinical laboratories and offers diagnostic testing and drug‑development services.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"LHX\": {\n        name: \"L3Harris\",\n        description: \"Provides defense communications, avionics and space sensors to government customers.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"LII\": {\n        name: \"Lennox International\",\n        description: \"Manufactures residential and commercial HVAC equipment and climate‑control solutions.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"LIN\": {\n        name: \"Linde plc\",\n        description: \"Produces and distributes industrial gases such as oxygen, hydrogen and nitrogen worldwide.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"LKQ\": {\n        name: \"LKQ Corporation\",\n        description: \"Distributes aftermarket and recycled auto parts for collision and mechanical repair.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"LLY\": {\n        name: \"Lilly (Eli)\",\n        description: \"Develops innovative pharmaceuticals for diabetes, oncology and neuroscience.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"LMT\": {\n        name: \"Lockheed Martin\",\n        description: \"Develops fighter jets, missiles and space systems for defense customers.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"LNT\": {\n        name: \"Alliant Energy\",\n        description: \"Provides regulated electric and natural‑gas utility service in Iowa and Wisconsin.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"LOW\": {\n        name: \"Lowe's\",\n        description: \"Operates home‑improvement retail stores selling hardware, appliances and building materials.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"LRCX\": {\n        name: \"Lam Research\",\n        description: \"Manufactures wafer‑fabrication equipment used in etch and deposition processes.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"LULU\": {\n        name: \"Lululemon Athletica\",\n        description: \"Designs and retails premium athletic apparel and accessories.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"LUV\": {\n        name: \"Southwest Airlines\",\n        description: \"Operates Southwest Airlines, a low‑cost carrier serving domestic and international routes.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"LVS\": {\n        name: \"Las Vegas Sands\",\n        description: \"Owns and operates integrated casino resorts in Macau and Singapore.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"LW\": {\n        name: \"Lamb Weston\",\n        description: \"Processes and sells frozen potato products to restaurants and retailers.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"LYB\": {\n        name: \"LyondellBasell\",\n        description: \"Produces polyolefin plastics, chemicals and refines crude oil derivatives.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"LYV\": {\n        name: \"Live Nation Entertainment\",\n        description: \"Promotes live concerts and operates Ticketmaster’s ticketing platform.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"MA\": {\n        name: \"Mastercard\",\n        description: \"Runs a global card network that processes electronic payments between banks and merchants.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"MAA\": {\n        name: \"Mid-America Apartment Communities\",\n        description: \"Owns and manages apartment communities across the Sun Belt.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"MAR\": {\n        name: \"Marriott International\",\n        description: \"Franchises and manages hotel brands including Marriott, Sheraton and Ritz‑Carlton.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"MAS\": {\n        name: \"Masco\",\n        description: \"Manufactures home‑improvement products such as faucets, paint and cabinetry.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"MCD\": {\n        name: \"McDonald's\",\n        description: \"Franchises and operates McDonald’s quick‑service hamburger restaurants worldwide.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"MCHP\": {\n        name: \"Microchip Technology\",\n        description: \"Designs microcontrollers, analog chips and secure connectivity solutions.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"MCK\": {\n        name: \"McKesson Corporation\",\n        description: \"Distributes pharmaceuticals and medical supplies to pharmacies and hospitals.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"MCO\": {\n        name: \"Moody's Corporation\",\n        description: \"Provides credit ratings, research and risk‑analytics data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"MDLZ\": {\n        name: \"Mondelez International\",\n        description: \"Markets snack brands like Oreo, Cadbury and Ritz crackers worldwide.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"MDT\": {\n        name: \"Medtronic\",\n        description: \"Develops implantable devices such as pacemakers and insulin pumps for chronic diseases.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"MET\": {\n        name: \"MetLife\",\n        description: \"Provides life insurance, annuities and employee benefits worldwide.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"META\": {\n        name: \"Meta Platforms\",\n        description: \"Owns Facebook, Instagram and WhatsApp social networks plus virtual‑reality hardware.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"MGM\": {\n        name: \"MGM Resorts\",\n        description: \"Owns and operates casino resorts and online sports‑betting platforms.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"MHK\": {\n        name: \"Mohawk Industries\",\n        description: \"Manufactures flooring products such as carpet, tile and laminate.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"MKC\": {\n        name: \"McCormick & Company\",\n        description: \"Produces spices, seasonings and flavorings for retail and food‑service customers.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"MKTX\": {\n        name: \"MarketAxess\",\n        description: \"Runs an electronic trading platform for corporate bonds and fixed‑income data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"MLM\": {\n        name: \"Martin Marietta Materials\",\n        description: \"Supplies aggregates, cement and asphalt used in commercial and infrastructure construction.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"MMC\": {\n        name: \"Marsh McLennan\",\n        description: \"Offers insurance brokerage, risk management and consulting through Marsh and Mercer units.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"MMM\": {\n        name: \"3M\",\n        description: \"Produces industrial abrasives, safety gear, healthcare supplies and consumer brands like Post‑it and Scotch.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"MNST\": {\n        name: \"Monster Beverage\",\n        description: \"Produces and markets Monster energy drinks and other non‑alcoholic beverages.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"MO\": {\n        name: \"Altria\",\n        description: \"Manufactures and sells Marlboro and other cigarette brands in the U.S.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"MOH\": {\n        name: \"Molina Healthcare\",\n        description: \"Provides managed‑care health plans focused on Medicaid and government programs.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"MOS\": {\n        name: \"Mosaic Company (The)\",\n        description: \"Produces potash and phosphate fertilizers for global agriculture.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"MPC\": {\n        name: \"Marathon Petroleum\",\n        description: \"Refines crude oil and operates gasoline stations and midstream pipeline assets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"MPWR\": {\n        name: \"Monolithic Power Systems\",\n        description: \"Designs power‑management semiconductors for automotive, industrial and cloud markets.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"MRK\": {\n        name: \"Merck & Co.\",\n        description: \"Researches and markets prescription medicines and vaccines for human and animal health.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"MRNA\": {\n        name: \"Moderna\",\n        description: \"Develops messenger‑RNA vaccines and therapeutics, including its COVID‑19 vaccine.\",\n        industry: \"Biotechnology\"\n    },\n    \"MS\": {\n        name: \"Morgan Stanley\",\n        description: \"Offers investment banking, trading and wealth management services.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"MSCI\": {\n        name: \"MSCI Inc.\",\n        description: \"Creates stock indices, ESG ratings and investment analytics software.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"MSFT\": {\n        name: \"Microsoft\",\n        description: \"Provides Windows, Office and Azure cloud computing services.\",\n        industry: \"Application Software\"\n    },\n    \"MSI\": {\n        name: \"Motorola Solutions\",\n        description: \"Supplies land‑mobile radios, software and services for public‑safety communications.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"MTB\": {\n        name: \"M&T Bank\",\n        description: \"Provides community banking, commercial lending and wealth management in the U.S. Northeast.\",\n        industry: \"Banks\"\n    },\n    \"MTCH\": {\n        name: \"Match Group\",\n        description: \"Operates dating apps including Tinder, Hinge and Match.com around the world.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"MTD\": {\n        name: \"Mettler Toledo\",\n        description: \"Supplies precision balances, analytical instruments and automated inspection systems.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"MU\": {\n        name: \"Micron Technology\",\n        description: \"Produces DRAM and NAND memory chips for computers, phones and data centers.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"NCLH\": {\n        name: \"Norwegian Cruise Line Holdings\",\n        description: \"Operates Norwegian, Oceania and Regent cruise brands offering global voyages.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"NDAQ\": {\n        name: \"Nasdaq, Inc.\",\n        description: \"Operates global equity and derivatives exchanges and market‑data services.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"NDSN\": {\n        name: \"Nordson Corporation\",\n        description: \"Makes precision dispensing and curing equipment for adhesives, coatings and sealants.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"NEE\": {\n        name: \"NextEra Energy\",\n        description: \"Generates and distributes electricity with a large portfolio of wind and solar assets.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"NEM\": {\n        name: \"Newmont\",\n        description: \"Mines gold and copper assets across the Americas, Africa and Australia.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"NFLX\": {\n        name: \"Netflix\",\n        description: \"Streams subscription video entertainment content worldwide.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"NI\": {\n        name: \"NiSource\",\n        description: \"Provides regulated natural‑gas and electric utility service in the U.S. Midwest.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"NKE\": {\n        name: \"Nike, Inc.\",\n        description: \"Designs and markets athletic footwear, apparel and equipment under the Nike and Jordan brands.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"NOC\": {\n        name: \"Northrop Grumman\",\n        description: \"Builds strategic defense systems including B‑21 bombers, satellites and missile defense.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"NOW\": {\n        name: \"ServiceNow\",\n        description: \"Provides cloud workflow automation and IT service management software as the ServiceNow platform.\",\n        industry: \"Application Software\"\n    },\n    \"NRG\": {\n        name: \"NRG Energy\",\n        description: \"Generates and sells electricity through a mix of natural‑gas, coal and renewable assets.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"NSC\": {\n        name: \"Norfolk Southern\",\n        description: \"Operates a major U.S. freight railroad serving the eastern United States.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"NTAP\": {\n        name: \"NetApp\",\n        description: \"Provides enterprise data‑storage hardware and cloud‑data management software.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"NTRS\": {\n        name: \"Northern Trust\",\n        description: \"Offers asset‑servicing, custody and wealth management to institutional and high‑net‑worth clients.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"NUE\": {\n        name: \"Nucor\",\n        description: \"Manufactures steel products from recycled scrap in electric arc furnaces.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"NVDA\": {\n        name: \"Nvidia\",\n        description: \"Designs GPUs and AI accelerators for gaming, data centers and autonomous vehicles.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"NVR\": {\n        name: \"NVR, Inc.\",\n        description: \"Constructs and sells single‑family homes and provides mortgage banking services.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"NWS\": {\n        name: \"News Corp (Class B)\",\n        description: \"Publishes Dow Jones, Wall Street Journal and digital real‑estate classifieds.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"NWSA\": {\n        name: \"News Corp (Class A)\",\n        description: \"Publishes Dow Jones, Wall Street Journal and digital real‑estate classifieds.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"NXPI\": {\n        name: \"NXP Semiconductors\",\n        description: \"Produces mixed‑signal chips for automotive, industrial and IoT applications.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"O\": {\n        name: \"Realty Income\",\n        description: \"Owns net‑lease retail and industrial properties and pays monthly dividends as a REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"ODFL\": {\n        name: \"Old Dominion\",\n        description: \"Provides nationwide less‑than‑truckload freight shipping and logistics.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"OKE\": {\n        name: \"Oneok\",\n        description: \"Operates natural‑gas liquids pipelines, processing and storage assets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"OMC\": {\n        name: \"Omnicom Group\",\n        description: \"Offers global advertising, public relations and marketing communication services.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"ON\": {\n        name: \"ON Semiconductor\",\n        description: \"Supplies power and sensing semiconductors for automotive and industrial markets.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"ORCL\": {\n        name: \"Oracle Corporation\",\n        description: \"Develops database software, business applications and cloud infrastructure services.\",\n        industry: \"Application Software\"\n    },\n    \"ORLY\": {\n        name: \"O’Reilly Automotive\",\n        description: \"Operates retail stores supplying aftermarket auto parts and accessories.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"OTIS\": {\n        name: \"Otis Worldwide\",\n        description: \"Designs, manufactures and services elevators and escalators worldwide.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"OXY\": {\n        name: \"Occidental Petroleum\",\n        description: \"Explores and produces oil and natural gas primarily in the U.S. and Middle East.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"PANW\": {\n        name: \"Palo Alto Networks\",\n        description: \"Offers next‑generation firewalls and cybersecurity platforms for enterprise networks.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"PARA\": {\n        name: \"Paramount Global\",\n        description: \"Operates the CBS television network, Paramount Pictures studio and streaming service Paramount+.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"PAYC\": {\n        name: \"Paycom\",\n        description: \"Delivers cloud‑based payroll and human‑capital‑management software for mid‑size companies.\",\n        industry: \"Application Software\"\n    },\n    \"PAYX\": {\n        name: \"Paychex\",\n        description: \"Provides cloud payroll processing and HR services to small and midsized businesses in the U.S.\",\n        industry: \"Cloud & IT Services\"\n    },\n    \"PCAR\": {\n        name: \"Paccar\",\n        description: \"Builds heavy‑duty trucks under the Kenworth, Peterbilt and DAF brands.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"PCG\": {\n        name: \"PG&E Corporation\",\n        description: \"Provides regulated electric and gas utility service to northern and central California.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"PEG\": {\n        name: \"Public Service Enterprise Group\",\n        description: \"Generates electricity and delivers gas and power to customers in New Jersey.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"PEP\": {\n        name: \"PepsiCo\",\n        description: \"Produces and distributes snack brands like Lay’s and beverages such as Pepsi and Gatorade.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"PFE\": {\n        name: \"Pfizer\",\n        description: \"Researches, manufactures and markets prescription drugs and vaccines worldwide.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"PFG\": {\n        name: \"Principal Financial Group\",\n        description: \"Provides retirement plans, life insurance and asset‑management services globally.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"PG\": {\n        name: \"Procter & Gamble\",\n        description: \"Markets household and personal‑care brands such as Tide, Pampers and Gillette worldwide.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"PGR\": {\n        name: \"Progressive Corporation\",\n        description: \"Underwrites auto, home and specialty insurance sold directly and through independent agents.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"PH\": {\n        name: \"Parker Hannifin\",\n        description: \"Supplies motion‑control systems, hydraulics and filtration products for industrial machinery.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"PHM\": {\n        name: \"PulteGroup\",\n        description: \"Builds single‑family homes and townhouses across U.S. growth markets under the Pulte brand.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PKG\": {\n        name: \"Packaging Corporation of America\",\n        description: \"Produces containerboard and corrugated packaging products for shipping goods.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PLD\": {\n        name: \"Prologis\",\n        description: \"Owns and develops logistics warehouses and distribution centers globally.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"PLTR\": {\n        name: \"Palantir Technologies\",\n        description: \"Provides data‑integration and analytics platforms for government and commercial clients.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"PM\": {\n        name: \"Philip Morris International\",\n        description: \"Sells Marlboro and other cigarette and heated‑tobacco products outside the United States.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"PNC\": {\n        name: \"PNC Financial Services\",\n        description: \"Offers commercial and retail banking, mortgage and asset‑management services across the U.S.\",\n        industry: \"Banks\"\n    },\n    \"PNR\": {\n        name: \"Pentair\",\n        description: \"Manufactures water pumps, filters and pool equipment for residential and industrial markets.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PNW\": {\n        name: \"Pinnacle West Capital\",\n        description: \"Generates and distributes electricity to customers in Arizona through its Arizona Public Service unit.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"PODD\": {\n        name: \"Insulet Corporation\",\n        description: \"Makes the tubeless Omnipod insulin pump for diabetes management.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"POOL\": {\n        name: \"Pool Corporation\",\n        description: \"Distributes swimming‑pool chemicals, equipment and outdoor living products to contractors and retailers.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"PPG\": {\n        name: \"PPG Industries\",\n        description: \"Produces paints, coatings and specialty materials for automotive, aerospace and industrial uses.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"PPL\": {\n        name: \"PPL Corporation\",\n        description: \"Owns and operates regulated electricity networks in Pennsylvania and Kentucky.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"PRU\": {\n        name: \"Prudential Financial\",\n        description: \"Provides life insurance, annuities and investment management under the Prudential brand.\",\n        industry: \"Insurance – Life & Health\"\n    },\n    \"PSA\": {\n        name: \"Public Storage\",\n        description: \"Operates the largest self‑storage REIT in the United States under the Public Storage brand.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"PSX\": {\n        name: \"Phillips 66\",\n        description: \"Refines crude oil and markets fuels, lubricants and petrochemicals primarily in the United States.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"PTC\": {\n        name: \"PTC Inc.\",\n        description: \"Offers computer‑aided design, product lifecycle and IoT software for industrial companies.\",\n        industry: \"Application Software\"\n    },\n    \"PWR\": {\n        name: \"Quanta Services\",\n        description: \"Provides engineering and construction services for electric transmission and renewable energy projects.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"PYPL\": {\n        name: \"PayPal\",\n        description: \"Runs the PayPal and Venmo digital wallets enabling online and peer‑to‑peer payments.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"QCOM\": {\n        name: \"Qualcomm\",\n        description: \"Designs mobile and automotive system‑on‑chip processors and licenses 5G wireless patents.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"RCL\": {\n        name: \"Royal Caribbean Group\",\n        description: \"Operates Royal Caribbean, Celebrity and Silversea cruise lines offering global vacations.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"REG\": {\n        name: \"Regency Centers\",\n        description: \"Owns grocery‑anchored open‑air shopping centers across the United States.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"REGN\": {\n        name: \"Regeneron Pharmaceuticals\",\n        description: \"Develops monoclonal antibody therapies for diseases such as macular degeneration and asthma.\",\n        industry: \"Biotechnology\"\n    },\n    \"RF\": {\n        name: \"Regions Financial Corporation\",\n        description: \"Operates a regional banking network across the Southeast and Midwest United States.\",\n        industry: \"Banks\"\n    },\n    \"RJF\": {\n        name: \"Raymond James Financial\",\n        description: \"Offers wealth management, investment banking and asset‑management services.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"RL\": {\n        name: \"Ralph Lauren Corporation\",\n        description: \"Designs and markets premium apparel, accessories and home goods under the Ralph Lauren brand.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"RMD\": {\n        name: \"ResMed\",\n        description: \"Manufactures sleep‑apnea devices and cloud‑connected respiratory care equipment.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"ROK\": {\n        name: \"Rockwell Automation\",\n        description: \"Produces industrial automation hardware and control software under the Allen‑Bradley brand.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"ROL\": {\n        name: \"Rollins, Inc.\",\n        description: \"Provides pest‑control and termite protection services to residential and commercial customers.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"ROP\": {\n        name: \"Roper Technologies\",\n        description: \"Acquires and operates niche industrial technology and software businesses in a decentralized model.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"ROST\": {\n        name: \"Ross Stores\",\n        description: \"Operates Ross Dress for Less off‑price retail stores selling branded apparel and home goods.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"RSG\": {\n        name: \"Republic Services\",\n        description: \"Provides solid‑waste collection, recycling and landfill services across North America.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"RTX\": {\n        name: \"RTX Corporation\",\n        description: \"Manufactures commercial jet engines, air defense systems and space propulsion technologies.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"RVTY\": {\n        name: \"Revvity\",\n        description: \"Supplies scientific instruments and diagnostics for life‑science research and newborn screening.\",\n        industry: \"Medical Devices – Diagnostics & Imaging\"\n    },\n    \"SBAC\": {\n        name: \"SBA Communications\",\n        description: \"Leases wireless communication towers to mobile carriers across the Americas.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"SBUX\": {\n        name: \"Starbucks\",\n        description: \"Operates the global Starbucks coffeehouse chain and branded ready‑to‑drink beverages.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"SCHW\": {\n        name: \"Charles Schwab\",\n        description: \"Provides discount brokerage, custody and financial advisory services for investors.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"SHW\": {\n        name: \"Sherwin-Williams\",\n        description: \"Produces architectural paints and coatings under the Sherwin‑Williams brand.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"SJM\": {\n        name: \"J.M. Smucker Company (The)\",\n        description: \"Manufactures branded food products such as Smucker's jams, Jif peanut butter and Folgers coffee.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"SLB\": {\n        name: \"Schlumberger\",\n        description: \"Provides drilling technology, reservoir evaluation and production services to energy producers.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"SMCI\": {\n        name: \"Supermicro\",\n        description: \"Designs and assembles high‑performance servers and storage systems for data centers.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"SNA\": {\n        name: \"Snap-on\",\n        description: \"Manufactures professional hand and power tools for the transportation and industrial sectors.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"SNPS\": {\n        name: \"Synopsys\",\n        description: \"Provides EDA software and IP used to design advanced semiconductor chips.\",\n        industry: \"Application Software\"\n    },\n    \"SO\": {\n        name: \"Southern Company\",\n        description: \"Generates and distributes electricity through regulated utilities across the southeastern U.S.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"SOLV\": {\n        name: \"Solventum\",\n        description: \"Provides medical sterilization and wound‑care products after the separation from 3M.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"SPG\": {\n        name: \"Simon Property Group\",\n        description: \"Owns and operates premier shopping malls and outlet centers across North America.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"SPGI\": {\n        name: \"S&P Global\",\n        description: \"Provides credit ratings, market indices and financial data through brands like S&P Global Ratings.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"SRE\": {\n        name: \"Sempra\",\n        description: \"Owns regulated natural‑gas and electric utilities in California and energy infrastructure assets.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"STE\": {\n        name: \"Steris\",\n        description: \"Supplies sterilization equipment and infection‑prevention consumables for hospitals.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"STLD\": {\n        name: \"Steel Dynamics\",\n        description: \"Produces flat‑rolled and recycled steel products for automotive and construction markets.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"STT\": {\n        name: \"State Street Corporation\",\n        description: \"Provides custody banking, ETF servicing and institutional asset management.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"STX\": {\n        name: \"Seagate Technology\",\n        description: \"Designs and manufactures hard‑disk drives and data‑storage solutions for enterprise and cloud.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"STZ\": {\n        name: \"Constellation Brands\",\n        description: \"Brews and imports Corona, Modelo and produces premium wine and spirits.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"SW\": {\n        name: \"Smurfit Westrock\",\n        description: \"Produces corrugated packaging and containerboard for consumer and industrial markets.\",\n        industry: \"Chemicals & Specialty Materials\"\n    },\n    \"SWK\": {\n        name: \"Stanley Black & Decker\",\n        description: \"Manufactures power tools and industrial fasteners under brands such as DeWalt and Stanley.\",\n        industry: \"Industrial Machinery – Tools & Components\"\n    },\n    \"SWKS\": {\n        name: \"Skyworks Solutions\",\n        description: \"Designs radio‑frequency chips used in smartphones and wireless infrastructure.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"SYF\": {\n        name: \"Synchrony Financial\",\n        description: \"Issues private‑label credit cards and point‑of‑sale financing for retailers.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"SYK\": {\n        name: \"Stryker Corporation\",\n        description: \"Develops orthopedic implants, surgical robots and hospital equipment.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"SYY\": {\n        name: \"Sysco\",\n        description: \"Distributes food and kitchen supplies to restaurants, healthcare and education customers.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"T\": {\n        name: \"AT&T\",\n        description: \"Operates wireless, broadband and pay‑TV networks across the United States.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"TAP\": {\n        name: \"Molson Coors Beverage Company\",\n        description: \"Brews and distributes beer brands including Coors Light and Miller Lite.\",\n        industry: \"Consumer Staples – Beverages\"\n    },\n    \"TDG\": {\n        name: \"TransDigm Group\",\n        description: \"Supplies highly engineered aircraft components and aftermarket parts.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"TDY\": {\n        name: \"Teledyne Technologies\",\n        description: \"Supplies specialty sensors, cameras and instruments for aerospace, defense and research.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"TECH\": {\n        name: \"Bio-Techne\",\n        description: \"Supplies proteins, antibodies and diagnostic reagents used in biomedical research.\",\n        industry: \"Biotechnology\"\n    },\n    \"TEL\": {\n        name: \"TE Connectivity\",\n        description: \"Produces electronic connectors and sensors for automotive, industrial and telecom uses.\",\n        industry: \"Electrical Equipment & Components\"\n    },\n    \"TER\": {\n        name: \"Teradyne\",\n        description: \"Manufactures semiconductor and electronics test equipment used in chip fabrication.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"TFC\": {\n        name: \"Truist Financial\",\n        description: \"Operates regional consumer and commercial banking and wealth services.\",\n        industry: \"Banks\"\n    },\n    \"TGT\": {\n        name: \"Target Corporation\",\n        description: \"Runs big‑box retail stores and an online marketplace selling general merchandise.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TJX\": {\n        name: \"TJX Companies\",\n        description: \"Operates off‑price retail chains including T.J. Maxx and Marshalls.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TKO\": {\n        name: \"TKO Group Holdings\",\n        description: \"Owns combat‑sports entertainment brands WWE and UFC.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"TMO\": {\n        name: \"Thermo Fisher Scientific\",\n        description: \"Provides lab instruments, reagents and contract manufacturing for life‑science research.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"TMUS\": {\n        name: \"T-Mobile US\",\n        description: \"Operates a nationwide wireless network offering mobile voice and data services.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"TPL\": {\n        name: \"Texas Pacific Land Corporation\",\n        description: \"Holds royalty interests and leases for oil‑rich land in Texas.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"TPR\": {\n        name: \"Tapestry, Inc.\",\n        description: \"Owns luxury lifestyle brands Coach, Kate Spade and Stuart Weitzman.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TRGP\": {\n        name: \"Targa Resources\",\n        description: \"Operates natural‑gas gathering, processing and NGL logistics assets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"TRMB\": {\n        name: \"Trimble Inc.\",\n        description: \"Provides GPS, laser and software systems for construction and agriculture automation.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"TROW\": {\n        name: \"T. Rowe Price\",\n        description: \"Manages mutual funds and retirement accounts for individual and institutional investors.\",\n        industry: \"Asset & Wealth Management\"\n    },\n    \"TRV\": {\n        name: \"Travelers Companies (The)\",\n        description: \"Underwrites commercial and personal property‑casualty insurance.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"TSCO\": {\n        name: \"Tractor Supply\",\n        description: \"Runs rural lifestyle retail stores selling farm supplies and pet products.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"TSLA\": {\n        name: \"Tesla, Inc.\",\n        description: \"Designs and sells electric vehicles, battery storage and solar energy products.\",\n        industry: \"Automobiles & Components\"\n    },\n    \"TSN\": {\n        name: \"Tyson Foods\",\n        description: \"Processes and markets chicken, beef and prepared food products.\",\n        industry: \"Consumer Staples – Packaged Foods & Meals\"\n    },\n    \"TT\": {\n        name: \"Trane Technologies\",\n        description: \"Manufactures HVAC systems and building climate solutions.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"TTWO\": {\n        name: \"Take-Two Interactive\",\n        description: \"Develops and publishes video games such as Grand Theft Auto and NBA 2K.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"TXN\": {\n        name: \"Texas Instruments\",\n        description: \"Designs and fabricates analog and embedded semiconductor chips for industrial and automotive markets.\",\n        industry: \"Semiconductors & Foundries\"\n    },\n    \"TXT\": {\n        name: \"Textron\",\n        description: \"Builds business jets, helicopters and defense vehicles under brands like Cessna and Bell.\",\n        industry: \"Aerospace & Defense\"\n    },\n    \"TYL\": {\n        name: \"Tyler Technologies\",\n        description: \"Delivers cloud software for local governments and courts.\",\n        industry: \"Application Software\"\n    },\n    \"UAL\": {\n        name: \"United Airlines Holdings\",\n        description: \"Provides scheduled air passenger and cargo transportation worldwide.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"UBER\": {\n        name: \"Uber\",\n        description: \"Offers ride‑hailing, food delivery and logistics services via its mobile platform.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"UDR\": {\n        name: \"UDR, Inc.\",\n        description: \"Owns and manages multifamily apartment communities across U.S. markets.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"UHS\": {\n        name: \"Universal Health Services\",\n        description: \"Operates acute‑care hospitals and behavioral health facilities.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"ULTA\": {\n        name: \"Ulta Beauty\",\n        description: \"Operates beauty retail stores and e‑commerce selling cosmetics and salon services.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"UNH\": {\n        name: \"UnitedHealth Group\",\n        description: \"Offers health insurance and operates Optum healthcare services.\",\n        industry: \"Healthcare Providers & Services\"\n    },\n    \"UNP\": {\n        name: \"Union Pacific Corporation\",\n        description: \"Runs one of North America’s largest freight railroad networks.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"UPS\": {\n        name: \"United Parcel Service\",\n        description: \"Delivers parcels and logistics services via an integrated global air‑ground network.\",\n        industry: \"Transportation & Logistics\"\n    },\n    \"URI\": {\n        name: \"United Rentals\",\n        description: \"Rents construction and industrial equipment through a nationwide branch network.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"USB\": {\n        name: \"U.S. Bancorp\",\n        description: \"Provides consumer and commercial banking, payment services and wealth management.\",\n        industry: \"Banks\"\n    },\n    \"V\": {\n        name: \"Visa Inc.\",\n        description: \"Operates a global card‑payment network connecting issuers, merchants and consumers.\",\n        industry: \"Digital Payments & FinTech\"\n    },\n    \"VICI\": {\n        name: \"Vici Properties\",\n        description: \"Owns casino and entertainment real estate leased to operators like Caesars.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"VLO\": {\n        name: \"Valero Energy\",\n        description: \"Refines crude oil into gasoline, diesel and specialty fuels for global markets.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"VLTO\": {\n        name: \"Veralto\",\n        description: \"Provides water‑quality testing and product‑identification equipment.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"VMC\": {\n        name: \"Vulcan Materials Company\",\n        description: \"Produces construction aggregates, asphalt and ready‑mixed concrete.\",\n        industry: \"Building Products – Materials\"\n    },\n    \"VRSK\": {\n        name: \"Verisk Analytics\",\n        description: \"Supplies data analytics and risk assessment tools for insurance and energy clients.\",\n        industry: \"Data & Analytics Platforms\"\n    },\n    \"VRSN\": {\n        name: \"Verisign\",\n        description: \"Operates .com and .net internet domain registries and related DNS infrastructure.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"VRTX\": {\n        name: \"Vertex Pharmaceuticals\",\n        description: \"Develops small‑molecule drugs for cystic fibrosis and other serious diseases.\",\n        industry: \"Biotechnology\"\n    },\n    \"VST\": {\n        name: \"Vistra Corp.\",\n        description: \"Generates and retails electricity primarily from natural gas and nuclear plants.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"VTR\": {\n        name: \"Ventas\",\n        description: \"Invests in senior housing and life‑science properties as a healthcare REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"VTRS\": {\n        name: \"Viatris\",\n        description: \"Manufactures and markets generic and biosimilar pharmaceuticals worldwide.\",\n        industry: \"Pharmaceuticals\"\n    },\n    \"VZ\": {\n        name: \"Verizon\",\n        description: \"Runs nationwide wireless, fiber broadband and enterprise telecom services.\",\n        industry: \"Telecom Operators & Infrastructure\"\n    },\n    \"WAB\": {\n        name: \"Wabtec\",\n        description: \"Manufactures locomotives and rail‑transit equipment and provides aftermarket services.\",\n        industry: \"Industrial Machinery – Heavy Equipment\"\n    },\n    \"WAT\": {\n        name: \"Waters Corporation\",\n        description: \"Provides liquid chromatography and mass‑spectrometry instruments for life‑science analysis.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"WBA\": {\n        name: \"Walgreens Boots Alliance\",\n        description: \"Operates retail pharmacy chains and distributes pharmaceutical products.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"WBD\": {\n        name: \"Warner Bros. Discovery\",\n        description: \"Operates cable networks, film studios and the Max streaming service.\",\n        industry: \"Media & Entertainment\"\n    },\n    \"WDAY\": {\n        name: \"Workday, Inc.\",\n        description: \"Offers cloud HR and financial management software for enterprises.\",\n        industry: \"Application Software\"\n    },\n    \"WDC\": {\n        name: \"Western Digital\",\n        description: \"Designs and manufactures HDD and flash‑based data‑storage devices.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"WEC\": {\n        name: \"WEC Energy Group\",\n        description: \"Generates and distributes electricity and gas to customers across Wisconsin and Illinois.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"WELL\": {\n        name: \"Welltower\",\n        description: \"Owns senior housing and medical office properties as a healthcare REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"WFC\": {\n        name: \"Wells Fargo\",\n        description: \"Offers consumer banking, mortgage lending and capital‑markets services.\",\n        industry: \"Banks\"\n    },\n    \"WM\": {\n        name: \"Waste Management\",\n        description: \"Collects, disposes and recycles municipal and industrial waste across North America.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"WMB\": {\n        name: \"Williams Companies\",\n        description: \"Operates natural‑gas pipelines, processing and storage assets across North America.\",\n        industry: \"Energy – Mid/Downstream & Services\"\n    },\n    \"WMT\": {\n        name: \"Walmart\",\n        description: \"Runs the world’s largest discount retail chain and e‑commerce platforms.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"WRB\": {\n        name: \"W. R. Berkley Corporation\",\n        description: \"Provides specialty commercial property‑casualty insurance nationwide.\",\n        industry: \"Insurance – Reinsurance & Specialty\"\n    },\n    \"WSM\": {\n        name: \"Williams-Sonoma, Inc.\",\n        description: \"Sells home furnishings online and through Pottery Barn and West Elm stores.\",\n        industry: \"Retail – Specialty – Lifestyle\"\n    },\n    \"WST\": {\n        name: \"West Pharmaceutical Services\",\n        description: \"Makes drug‑delivery components such as vial stoppers and syringes.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"WTW\": {\n        name: \"Willis Towers Watson\",\n        description: \"Provides insurance brokerage and human‑capital consulting services.\",\n        industry: \"Insurance – P&C\"\n    },\n    \"WY\": {\n        name: \"Weyerhaeuser\",\n        description: \"Manages timberlands and produces wood products as a timber REIT.\",\n        industry: \"Real Estate – Commercial REITs\"\n    },\n    \"WYNN\": {\n        name: \"Wynn Resorts\",\n        description: \"Develops and operates luxury casino resorts in Las Vegas and Macau.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"XEL\": {\n        name: \"Xcel Energy\",\n        description: \"Generates and distributes electricity and natural gas across eight U.S. states.\",\n        industry: \"Utilities – Gas Utilities\"\n    },\n    \"XOM\": {\n        name: \"ExxonMobil\",\n        description: \"Engages in worldwide exploration, production, refining and marketing of oil and natural gas.\",\n        industry: \"Energy – Upstream (Oil & Gas E&P)\"\n    },\n    \"XYL\": {\n        name: \"Xylem Inc.\",\n        description: \"Provides water pumps, meters and analytics for water infrastructure management.\",\n        industry: \"Utilities – Electric Utilities\"\n    },\n    \"YUM\": {\n        name: \"Yum! Brands\",\n        description: \"Franchises KFC, Pizza Hut and Taco Bell quick‑service restaurant chains worldwide.\",\n        industry: \"Hotels, Resorts & Leisure\"\n    },\n    \"ZBH\": {\n        name: \"Zimmer Biomet\",\n        description: \"Manufactures orthopedic implants and surgical devices for joint replacement.\",\n        industry: \"Medical Devices – Lab Instruments\"\n    },\n    \"ZBRA\": {\n        name: \"Zebra Technologies\",\n        description: \"Produces barcode printers, mobile computers and RFID solutions for supply‑chain tracking.\",\n        industry: \"Computer & Networking Hardware\"\n    },\n    \"ZTS\": {\n        name: \"Zoetis\",\n        description: \"Develops and sells vaccines and medicines for livestock and pets.\",\n        industry: \"Pharmaceuticals\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/data/sp500_enriched_final.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/save_analysis_results.ts":
/*!************************************************!*\
  !*** ./src/pages/api/save_analysis_results.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _utils_resultsStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/resultsStorage */ \"(api)/./src/utils/resultsStorage.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const results = req.body;\n        if (!results.symbol) {\n            return res.status(400).json({\n                error: 'Symbol is required'\n            });\n        }\n        const filepath = await (0,_utils_resultsStorage__WEBPACK_IMPORTED_MODULE_0__.saveAnalysisResults)(results);\n        res.status(200).json({\n            success: true,\n            message: 'Analysis results saved successfully',\n            filepath\n        });\n    } catch (error) {\n        console.error('Error saving analysis results:', error);\n        res.status(500).json({\n            error: 'Failed to save analysis results',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/save_analysis_results.ts\n");

/***/ }),

/***/ "(api)/./src/utils/companyLookup.ts":
/*!************************************!*\
  !*** ./src/utils/companyLookup.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllTickers: () => (/* binding */ getAllTickers),\n/* harmony export */   getCompanyInfo: () => (/* binding */ getCompanyInfo),\n/* harmony export */   getCompanyName: () => (/* binding */ getCompanyName),\n/* harmony export */   isValidTicker: () => (/* binding */ isValidTicker),\n/* harmony export */   searchCompaniesByName: () => (/* binding */ searchCompaniesByName)\n/* harmony export */ });\n/* harmony import */ var _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../data/sp500_enriched_final */ \"(api)/./src/data/sp500_enriched_final.ts\");\n/**\n * Company name lookup utility for S&P 500 companies\n * Provides functions to get company names from ticker symbols\n */ \n/**\n * Get company name from ticker symbol\n * @param ticker - Stock ticker symbol (e.g., \"AAPL\")\n * @returns Company name or ticker if not found\n */ function getCompanyName(ticker) {\n    const upperTicker = ticker.toUpperCase();\n    const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_0__.QUICK_ENRICHED_FINAL[upperTicker];\n    return company?.name || ticker;\n}\n/**\n * Get full company information from ticker symbol\n * @param ticker - Stock ticker symbol (e.g., \"AAPL\")\n * @returns Company information object or null if not found\n */ function getCompanyInfo(ticker) {\n    const upperTicker = ticker.toUpperCase();\n    const company = _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_0__.QUICK_ENRICHED_FINAL[upperTicker];\n    return company || null;\n}\n/**\n * Check if a ticker exists in the S&P 500 data\n * @param ticker - Stock ticker symbol\n * @returns true if ticker exists, false otherwise\n */ function isValidTicker(ticker) {\n    const upperTicker = ticker.toUpperCase();\n    return upperTicker in _data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_0__.QUICK_ENRICHED_FINAL;\n}\n/**\n * Get all available tickers\n * @returns Array of all ticker symbols\n */ function getAllTickers() {\n    return Object.keys(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_0__.QUICK_ENRICHED_FINAL);\n}\n/**\n * Search companies by name (case-insensitive partial match)\n * @param searchTerm - Search term to match against company names\n * @returns Array of matching ticker symbols\n */ function searchCompaniesByName(searchTerm) {\n    const lowerSearchTerm = searchTerm.toLowerCase();\n    const matches = [];\n    for (const [ticker, company] of Object.entries(_data_sp500_enriched_final__WEBPACK_IMPORTED_MODULE_0__.QUICK_ENRICHED_FINAL)){\n        if (company.name.toLowerCase().includes(lowerSearchTerm)) {\n            matches.push(ticker);\n        }\n    }\n    return matches;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/utils/companyLookup.ts\n");

/***/ }),

/***/ "(api)/./src/utils/resultsStorage.ts":
/*!*************************************!*\
  !*** ./src/utils/resultsStorage.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupOldResults: () => (/* binding */ cleanupOldResults),\n/* harmony export */   getAllAnalysisResults: () => (/* binding */ getAllAnalysisResults),\n/* harmony export */   loadAnalysisResults: () => (/* binding */ loadAnalysisResults),\n/* harmony export */   saveAnalysisResults: () => (/* binding */ saveAnalysisResults)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _companyLookup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./companyLookup */ \"(api)/./src/utils/companyLookup.ts\");\n/**\n * Results storage utility for SpeedTraffic analysis data\n * Optimized for fine-tuning API consumption and ML model training workflows\n */ \n\n\n/**\n * Save comprehensive analysis results to JSON file\n * @param results - Complete analysis results object\n * @returns Promise<string> - Path to saved file\n */ async function saveAnalysisResults(results) {\n    try {\n        // Ensure results directory exists\n        const resultsDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'results');\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(resultsDir)) {\n            fs__WEBPACK_IMPORTED_MODULE_0___default().mkdirSync(resultsDir, {\n                recursive: true\n            });\n        }\n        // Generate filename: SYMBOL_YYYY-MM-DD_HH-mm-ss.json\n        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);\n        const filename = `${results.symbol}_${timestamp}.json`;\n        const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultsDir, filename);\n        // Add metadata\n        const enrichedResults = {\n            ...results,\n            companyName: (0,_companyLookup__WEBPACK_IMPORTED_MODULE_2__.getCompanyName)(results.symbol),\n            timestamp: new Date().toISOString(),\n            analysisDate: new Date().toISOString().split('T')[0]\n        };\n        // Write to file (overwrite if exists)\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(filepath, JSON.stringify(enrichedResults, null, 2), 'utf8');\n        console.log(`Analysis results saved to: ${filepath}`);\n        return filepath;\n    } catch (error) {\n        console.error('Error saving analysis results:', error);\n        throw error;\n    }\n}\n/**\n * Load analysis results from JSON file\n * @param symbol - Stock symbol\n * @param timestamp - Optional timestamp, if not provided loads most recent\n * @returns Promise<AnalysisResults | null>\n */ async function loadAnalysisResults(symbol, timestamp) {\n    try {\n        const resultsDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'results');\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(resultsDir)) {\n            return null;\n        }\n        // Find matching files\n        const files = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(resultsDir).filter((file)=>file.startsWith(`${symbol}_`) && file.endsWith('.json')).sort().reverse(); // Most recent first\n        if (files.length === 0) {\n            return null;\n        }\n        // Use specific timestamp or most recent\n        const targetFile = timestamp ? files.find((file)=>file.includes(timestamp)) : files[0];\n        if (!targetFile) {\n            return null;\n        }\n        const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultsDir, targetFile);\n        const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filepath, 'utf8');\n        return JSON.parse(data);\n    } catch (error) {\n        console.error('Error loading analysis results:', error);\n        return null;\n    }\n}\n/**\n * Get all analysis results for a symbol\n * @param symbol - Stock symbol\n * @returns Promise<AnalysisResults[]>\n */ async function getAllAnalysisResults(symbol) {\n    try {\n        const resultsDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'results');\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(resultsDir)) {\n            return [];\n        }\n        const files = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(resultsDir).filter((file)=>file.startsWith(`${symbol}_`) && file.endsWith('.json')).sort().reverse(); // Most recent first\n        const results = [];\n        for (const file of files){\n            try {\n                const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultsDir, file);\n                const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(filepath, 'utf8');\n                results.push(JSON.parse(data));\n            } catch (error) {\n                console.error(`Error reading file ${file}:`, error);\n            }\n        }\n        return results;\n    } catch (error) {\n        console.error('Error getting all analysis results:', error);\n        return [];\n    }\n}\n/**\n * Delete old analysis results, keeping only the most recent N files per symbol\n * @param symbol - Stock symbol\n * @param keepCount - Number of recent files to keep (default: 5)\n * @returns Promise<number> - Number of files deleted\n */ async function cleanupOldResults(symbol, keepCount = 5) {\n    try {\n        const resultsDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'results');\n        if (!fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(resultsDir)) {\n            return 0;\n        }\n        const files = fs__WEBPACK_IMPORTED_MODULE_0___default().readdirSync(resultsDir).filter((file)=>file.startsWith(`${symbol}_`) && file.endsWith('.json')).sort().reverse(); // Most recent first\n        const filesToDelete = files.slice(keepCount);\n        let deletedCount = 0;\n        for (const file of filesToDelete){\n            try {\n                const filepath = path__WEBPACK_IMPORTED_MODULE_1___default().join(resultsDir, file);\n                fs__WEBPACK_IMPORTED_MODULE_0___default().unlinkSync(filepath);\n                deletedCount++;\n            } catch (error) {\n                console.error(`Error deleting file ${file}:`, error);\n            }\n        }\n        return deletedCount;\n    } catch (error) {\n        console.error('Error cleaning up old results:', error);\n        return 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvdXRpbHMvcmVzdWx0c1N0b3JhZ2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUE7OztDQUdDLEdBRW1CO0FBQ0k7QUFDeUI7QUE4RmpEOzs7O0NBSUMsR0FDTSxlQUFlRyxvQkFBb0JDLE9BQXdCO0lBQ2hFLElBQUk7UUFDRixrQ0FBa0M7UUFDbEMsTUFBTUMsYUFBYUosZ0RBQVMsQ0FBQ00sUUFBUUMsR0FBRyxJQUFJLE9BQU8sUUFBUTtRQUMzRCxJQUFJLENBQUNSLG9EQUFhLENBQUNLLGFBQWE7WUFDOUJMLG1EQUFZLENBQUNLLFlBQVk7Z0JBQUVNLFdBQVc7WUFBSztRQUM3QztRQUVBLHFEQUFxRDtRQUNyRCxNQUFNQyxZQUFZLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsT0FBTyxDQUFDLFNBQVMsS0FBS0MsS0FBSyxDQUFDLEdBQUc7UUFDMUUsTUFBTUMsV0FBVyxHQUFHYixRQUFRYyxNQUFNLENBQUMsQ0FBQyxFQUFFTixVQUFVLEtBQUssQ0FBQztRQUN0RCxNQUFNTyxXQUFXbEIsZ0RBQVMsQ0FBQ0ksWUFBWVk7UUFFdkMsZUFBZTtRQUNmLE1BQU1HLGtCQUFtQztZQUN2QyxHQUFHaEIsT0FBTztZQUNWaUIsYUFBYW5CLDhEQUFjQSxDQUFDRSxRQUFRYyxNQUFNO1lBQzFDTixXQUFXLElBQUlDLE9BQU9DLFdBQVc7WUFDakNRLGNBQWMsSUFBSVQsT0FBT0MsV0FBVyxHQUFHUyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7UUFDdEQ7UUFFQSxzQ0FBc0M7UUFDdEN2Qix1REFBZ0IsQ0FBQ21CLFVBQVVNLEtBQUtDLFNBQVMsQ0FBQ04saUJBQWlCLE1BQU0sSUFBSTtRQUVyRU8sUUFBUUMsR0FBRyxDQUFDLENBQUMsMkJBQTJCLEVBQUVULFVBQVU7UUFDcEQsT0FBT0E7SUFFVCxFQUFFLE9BQU9VLE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7O0NBS0MsR0FDTSxlQUFlQyxvQkFBb0JaLE1BQWMsRUFBRU4sU0FBa0I7SUFDMUUsSUFBSTtRQUNGLE1BQU1QLGFBQWFKLGdEQUFTLENBQUNNLFFBQVFDLEdBQUcsSUFBSSxPQUFPLFFBQVE7UUFFM0QsSUFBSSxDQUFDUixvREFBYSxDQUFDSyxhQUFhO1lBQzlCLE9BQU87UUFDVDtRQUVBLHNCQUFzQjtRQUN0QixNQUFNMEIsUUFBUS9CLHFEQUFjLENBQUNLLFlBQzFCNEIsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxVQUFVLENBQUMsR0FBR2pCLE9BQU8sQ0FBQyxDQUFDLEtBQUtnQixLQUFLRSxRQUFRLENBQUMsVUFDOURDLElBQUksR0FDSkMsT0FBTyxJQUFJLG9CQUFvQjtRQUVsQyxJQUFJUCxNQUFNUSxNQUFNLEtBQUssR0FBRztZQUN0QixPQUFPO1FBQ1Q7UUFFQSx3Q0FBd0M7UUFDeEMsTUFBTUMsYUFBYTVCLFlBQ2ZtQixNQUFNVSxJQUFJLENBQUNQLENBQUFBLE9BQVFBLEtBQUtRLFFBQVEsQ0FBQzlCLGNBQ2pDbUIsS0FBSyxDQUFDLEVBQUU7UUFFWixJQUFJLENBQUNTLFlBQVk7WUFDZixPQUFPO1FBQ1Q7UUFFQSxNQUFNckIsV0FBV2xCLGdEQUFTLENBQUNJLFlBQVltQztRQUN2QyxNQUFNRyxPQUFPM0Msc0RBQWUsQ0FBQ21CLFVBQVU7UUFDdkMsT0FBT00sS0FBS29CLEtBQUssQ0FBQ0Y7SUFFcEIsRUFBRSxPQUFPZCxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxtQ0FBbUNBO1FBQ2pELE9BQU87SUFDVDtBQUNGO0FBRUE7Ozs7Q0FJQyxHQUNNLGVBQWVpQixzQkFBc0I1QixNQUFjO0lBQ3hELElBQUk7UUFDRixNQUFNYixhQUFhSixnREFBUyxDQUFDTSxRQUFRQyxHQUFHLElBQUksT0FBTyxRQUFRO1FBRTNELElBQUksQ0FBQ1Isb0RBQWEsQ0FBQ0ssYUFBYTtZQUM5QixPQUFPLEVBQUU7UUFDWDtRQUVBLE1BQU0wQixRQUFRL0IscURBQWMsQ0FBQ0ssWUFDMUI0QixNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLFVBQVUsQ0FBQyxHQUFHakIsT0FBTyxDQUFDLENBQUMsS0FBS2dCLEtBQUtFLFFBQVEsQ0FBQyxVQUM5REMsSUFBSSxHQUNKQyxPQUFPLElBQUksb0JBQW9CO1FBRWxDLE1BQU1sQyxVQUE2QixFQUFFO1FBRXJDLEtBQUssTUFBTThCLFFBQVFILE1BQU87WUFDeEIsSUFBSTtnQkFDRixNQUFNWixXQUFXbEIsZ0RBQVMsQ0FBQ0ksWUFBWTZCO2dCQUN2QyxNQUFNUyxPQUFPM0Msc0RBQWUsQ0FBQ21CLFVBQVU7Z0JBQ3ZDZixRQUFRMkMsSUFBSSxDQUFDdEIsS0FBS29CLEtBQUssQ0FBQ0Y7WUFDMUIsRUFBRSxPQUFPZCxPQUFPO2dCQUNkRixRQUFRRSxLQUFLLENBQUMsQ0FBQyxtQkFBbUIsRUFBRUssS0FBSyxDQUFDLENBQUMsRUFBRUw7WUFDL0M7UUFDRjtRQUVBLE9BQU96QjtJQUVULEVBQUUsT0FBT3lCLE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLHVDQUF1Q0E7UUFDckQsT0FBTyxFQUFFO0lBQ1g7QUFDRjtBQUVBOzs7OztDQUtDLEdBQ00sZUFBZW1CLGtCQUFrQjlCLE1BQWMsRUFBRStCLFlBQW9CLENBQUM7SUFDM0UsSUFBSTtRQUNGLE1BQU01QyxhQUFhSixnREFBUyxDQUFDTSxRQUFRQyxHQUFHLElBQUksT0FBTyxRQUFRO1FBRTNELElBQUksQ0FBQ1Isb0RBQWEsQ0FBQ0ssYUFBYTtZQUM5QixPQUFPO1FBQ1Q7UUFFQSxNQUFNMEIsUUFBUS9CLHFEQUFjLENBQUNLLFlBQzFCNEIsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxVQUFVLENBQUMsR0FBR2pCLE9BQU8sQ0FBQyxDQUFDLEtBQUtnQixLQUFLRSxRQUFRLENBQUMsVUFDOURDLElBQUksR0FDSkMsT0FBTyxJQUFJLG9CQUFvQjtRQUVsQyxNQUFNWSxnQkFBZ0JuQixNQUFNZixLQUFLLENBQUNpQztRQUNsQyxJQUFJRSxlQUFlO1FBRW5CLEtBQUssTUFBTWpCLFFBQVFnQixjQUFlO1lBQ2hDLElBQUk7Z0JBQ0YsTUFBTS9CLFdBQVdsQixnREFBUyxDQUFDSSxZQUFZNkI7Z0JBQ3ZDbEMsb0RBQWEsQ0FBQ21CO2dCQUNkZ0M7WUFDRixFQUFFLE9BQU90QixPQUFPO2dCQUNkRixRQUFRRSxLQUFLLENBQUMsQ0FBQyxvQkFBb0IsRUFBRUssS0FBSyxDQUFDLENBQUMsRUFBRUw7WUFDaEQ7UUFDRjtRQUVBLE9BQU9zQjtJQUVULEVBQUUsT0FBT3RCLE9BQU87UUFDZEYsUUFBUUUsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29uZzdcXERlc2t0b3BcXGhvbWVcXHVidW50dVxcZmluYW5jaWFsX2Rhc2hib2FyZFxcc3JjXFx1dGlsc1xccmVzdWx0c1N0b3JhZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXN1bHRzIHN0b3JhZ2UgdXRpbGl0eSBmb3IgU3BlZWRUcmFmZmljIGFuYWx5c2lzIGRhdGFcbiAqIE9wdGltaXplZCBmb3IgZmluZS10dW5pbmcgQVBJIGNvbnN1bXB0aW9uIGFuZCBNTCBtb2RlbCB0cmFpbmluZyB3b3JrZmxvd3NcbiAqL1xuXG5pbXBvcnQgZnMgZnJvbSAnZnMnO1xuaW1wb3J0IHBhdGggZnJvbSAncGF0aCc7XG5pbXBvcnQgeyBnZXRDb21wYW55TmFtZSB9IGZyb20gJy4vY29tcGFueUxvb2t1cCc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgQW5hbHlzaXNSZXN1bHRzIHtcbiAgLy8gTWV0YWRhdGFcbiAgc3ltYm9sOiBzdHJpbmc7XG4gIGNvbXBhbnlOYW1lOiBzdHJpbmc7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBhbmFseXNpc0RhdGU6IHN0cmluZztcbiAgXG4gIC8vIFNlcnZpY2UgUmVzdWx0c1xuICBsc3RtPzoge1xuICAgIHN5bWJvbDogc3RyaW5nO1xuICAgIHRyYWluX3VudGlsOiBzdHJpbmc7XG4gICAgcHJlZGljdGlvbnM6IEFycmF5PHtcbiAgICAgIGRhdGU6IHN0cmluZztcbiAgICAgIHByZWRfcHJvYl91cDogbnVtYmVyO1xuICAgICAgcHJlZF9wcm9iX2Rvd246IG51bWJlcjtcbiAgICAgIHByZWRpY3RlZF9sYWJlbDogbnVtYmVyO1xuICAgICAgYWN0dWFsX2xhYmVsOiBudW1iZXIgfCBudWxsO1xuICAgICAgcHJlZGljdGlvbl9ob3Jpem9uOiBudW1iZXI7XG4gICAgfT47XG4gICAgdHJhZmZpY19saWdodDogc3RyaW5nO1xuICAgIG1ldHJpY3M6IHtcbiAgICAgIGFjY3VyYWN5PzogbnVtYmVyO1xuICAgICAgcHJlY2lzaW9uPzogbnVtYmVyO1xuICAgICAgcmVjYWxsPzogbnVtYmVyO1xuICAgICAgZjFfc2NvcmU/OiBudW1iZXI7XG4gICAgfTtcbiAgICBzdW1tYXJ5X2tvPzogc3RyaW5nO1xuICB9O1xuICBcbiAgbWZpPzoge1xuICAgIHN5bWJvbDogc3RyaW5nO1xuICAgIGRhdGU6IHN0cmluZztcbiAgICBtZmlfMTQ6IG51bWJlcjtcbiAgICB0cmFmZmljX2xpZ2h0OiBzdHJpbmc7XG4gICAgc2lnbmFsOiBzdHJpbmc7XG4gICAgc3VtbWFyeV9rbz86IHN0cmluZztcbiAgfTtcbiAgXG4gIGJvbGxpbmdlcj86IHtcbiAgICBzeW1ib2w6IHN0cmluZztcbiAgICBkYXRlOiBzdHJpbmc7XG4gICAgdHJhZmZpY19saWdodDogc3RyaW5nO1xuICAgIHNpZ25hbDogc3RyaW5nO1xuICAgIHN1bW1hcnlfa28/OiBzdHJpbmc7XG4gIH07XG4gIFxuICByc2k/OiB7XG4gICAgc3ltYm9sOiBzdHJpbmc7XG4gICAgZGF0ZTogc3RyaW5nO1xuICAgIHRyYWZmaWNfbGlnaHQ6IHN0cmluZztcbiAgICBzaWduYWw6IHN0cmluZztcbiAgICBzdW1tYXJ5X2tvPzogc3RyaW5nO1xuICB9O1xuICBcbiAgaW5kdXN0cnk/OiB7XG4gICAgc3ltYm9sOiBzdHJpbmc7XG4gICAgZGF0ZTogc3RyaW5nO1xuICAgIHRyYWZmaWNfbGlnaHQ6IHN0cmluZztcbiAgICBzaWduYWw6IHN0cmluZztcbiAgICBzdW1tYXJ5X2tvPzogc3RyaW5nO1xuICB9O1xuICBcbiAgY2FwbT86IHtcbiAgICBzeW1ib2w6IHN0cmluZztcbiAgICBkYXRlOiBzdHJpbmc7XG4gICAgdHJhZmZpY19saWdodDogc3RyaW5nO1xuICAgIHNpZ25hbDogc3RyaW5nO1xuICAgIHN1bW1hcnlfa28/OiBzdHJpbmc7XG4gIH07XG4gIFxuICBnYXJjaD86IHtcbiAgICBzeW1ib2w6IHN0cmluZztcbiAgICBkYXRlOiBzdHJpbmc7XG4gICAgdHJhZmZpY19saWdodDogc3RyaW5nO1xuICAgIHNpZ25hbDogc3RyaW5nO1xuICAgIHN1bW1hcnlfa28/OiBzdHJpbmc7XG4gIH07XG4gIFxuICAvLyBUcmFmZmljIExpZ2h0IFN1bW1hcnlcbiAgdHJhZmZpY19saWdodHM6IHtcbiAgICB0ZWNobmljYWw/OiBzdHJpbmc7XG4gICAgaW5kdXN0cnk/OiBzdHJpbmc7XG4gICAgbWFya2V0Pzogc3RyaW5nO1xuICAgIHJpc2s/OiBzdHJpbmc7XG4gICAgbmV1cmFsPzogc3RyaW5nO1xuICB9O1xuICBcbiAgLy8gUGVyZm9ybWFuY2UgU3VtbWFyeVxuICBvdmVyYWxsX3NpZ25hbD86IHN0cmluZztcbiAgY29uZmlkZW5jZV9zY29yZT86IG51bWJlcjtcbn1cblxuLyoqXG4gKiBTYXZlIGNvbXByZWhlbnNpdmUgYW5hbHlzaXMgcmVzdWx0cyB0byBKU09OIGZpbGVcbiAqIEBwYXJhbSByZXN1bHRzIC0gQ29tcGxldGUgYW5hbHlzaXMgcmVzdWx0cyBvYmplY3RcbiAqIEByZXR1cm5zIFByb21pc2U8c3RyaW5nPiAtIFBhdGggdG8gc2F2ZWQgZmlsZVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2F2ZUFuYWx5c2lzUmVzdWx0cyhyZXN1bHRzOiBBbmFseXNpc1Jlc3VsdHMpOiBQcm9taXNlPHN0cmluZz4ge1xuICB0cnkge1xuICAgIC8vIEVuc3VyZSByZXN1bHRzIGRpcmVjdG9yeSBleGlzdHNcbiAgICBjb25zdCByZXN1bHRzRGlyID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdzcmMnLCAnZGF0YScsICdyZXN1bHRzJyk7XG4gICAgaWYgKCFmcy5leGlzdHNTeW5jKHJlc3VsdHNEaXIpKSB7XG4gICAgICBmcy5ta2RpclN5bmMocmVzdWx0c0RpciwgeyByZWN1cnNpdmU6IHRydWUgfSk7XG4gICAgfVxuICAgIFxuICAgIC8vIEdlbmVyYXRlIGZpbGVuYW1lOiBTWU1CT0xfWVlZWS1NTS1ERF9ISC1tbS1zcy5qc29uXG4gICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnJlcGxhY2UoL1s6Ll0vZywgJy0nKS5zbGljZSgwLCAxOSk7XG4gICAgY29uc3QgZmlsZW5hbWUgPSBgJHtyZXN1bHRzLnN5bWJvbH1fJHt0aW1lc3RhbXB9Lmpzb25gO1xuICAgIGNvbnN0IGZpbGVwYXRoID0gcGF0aC5qb2luKHJlc3VsdHNEaXIsIGZpbGVuYW1lKTtcbiAgICBcbiAgICAvLyBBZGQgbWV0YWRhdGFcbiAgICBjb25zdCBlbnJpY2hlZFJlc3VsdHM6IEFuYWx5c2lzUmVzdWx0cyA9IHtcbiAgICAgIC4uLnJlc3VsdHMsXG4gICAgICBjb21wYW55TmFtZTogZ2V0Q29tcGFueU5hbWUocmVzdWx0cy5zeW1ib2wpLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICBhbmFseXNpc0RhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgIH07XG4gICAgXG4gICAgLy8gV3JpdGUgdG8gZmlsZSAob3ZlcndyaXRlIGlmIGV4aXN0cylcbiAgICBmcy53cml0ZUZpbGVTeW5jKGZpbGVwYXRoLCBKU09OLnN0cmluZ2lmeShlbnJpY2hlZFJlc3VsdHMsIG51bGwsIDIpLCAndXRmOCcpO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKGBBbmFseXNpcyByZXN1bHRzIHNhdmVkIHRvOiAke2ZpbGVwYXRofWApO1xuICAgIHJldHVybiBmaWxlcGF0aDtcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgYW5hbHlzaXMgcmVzdWx0czonLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBMb2FkIGFuYWx5c2lzIHJlc3VsdHMgZnJvbSBKU09OIGZpbGVcbiAqIEBwYXJhbSBzeW1ib2wgLSBTdG9jayBzeW1ib2xcbiAqIEBwYXJhbSB0aW1lc3RhbXAgLSBPcHRpb25hbCB0aW1lc3RhbXAsIGlmIG5vdCBwcm92aWRlZCBsb2FkcyBtb3N0IHJlY2VudFxuICogQHJldHVybnMgUHJvbWlzZTxBbmFseXNpc1Jlc3VsdHMgfCBudWxsPlxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbG9hZEFuYWx5c2lzUmVzdWx0cyhzeW1ib2w6IHN0cmluZywgdGltZXN0YW1wPzogc3RyaW5nKTogUHJvbWlzZTxBbmFseXNpc1Jlc3VsdHMgfCBudWxsPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0c0RpciA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAnc3JjJywgJ2RhdGEnLCAncmVzdWx0cycpO1xuICAgIFxuICAgIGlmICghZnMuZXhpc3RzU3luYyhyZXN1bHRzRGlyKSkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIFxuICAgIC8vIEZpbmQgbWF0Y2hpbmcgZmlsZXNcbiAgICBjb25zdCBmaWxlcyA9IGZzLnJlYWRkaXJTeW5jKHJlc3VsdHNEaXIpXG4gICAgICAuZmlsdGVyKGZpbGUgPT4gZmlsZS5zdGFydHNXaXRoKGAke3N5bWJvbH1fYCkgJiYgZmlsZS5lbmRzV2l0aCgnLmpzb24nKSlcbiAgICAgIC5zb3J0KClcbiAgICAgIC5yZXZlcnNlKCk7IC8vIE1vc3QgcmVjZW50IGZpcnN0XG4gICAgXG4gICAgaWYgKGZpbGVzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIFxuICAgIC8vIFVzZSBzcGVjaWZpYyB0aW1lc3RhbXAgb3IgbW9zdCByZWNlbnRcbiAgICBjb25zdCB0YXJnZXRGaWxlID0gdGltZXN0YW1wIFxuICAgICAgPyBmaWxlcy5maW5kKGZpbGUgPT4gZmlsZS5pbmNsdWRlcyh0aW1lc3RhbXApKVxuICAgICAgOiBmaWxlc1swXTtcbiAgICBcbiAgICBpZiAoIXRhcmdldEZpbGUpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBmaWxlcGF0aCA9IHBhdGguam9pbihyZXN1bHRzRGlyLCB0YXJnZXRGaWxlKTtcbiAgICBjb25zdCBkYXRhID0gZnMucmVhZEZpbGVTeW5jKGZpbGVwYXRoLCAndXRmOCcpO1xuICAgIHJldHVybiBKU09OLnBhcnNlKGRhdGEpIGFzIEFuYWx5c2lzUmVzdWx0cztcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGFuYWx5c2lzIHJlc3VsdHM6JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogR2V0IGFsbCBhbmFseXNpcyByZXN1bHRzIGZvciBhIHN5bWJvbFxuICogQHBhcmFtIHN5bWJvbCAtIFN0b2NrIHN5bWJvbFxuICogQHJldHVybnMgUHJvbWlzZTxBbmFseXNpc1Jlc3VsdHNbXT5cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEFsbEFuYWx5c2lzUmVzdWx0cyhzeW1ib2w6IHN0cmluZyk6IFByb21pc2U8QW5hbHlzaXNSZXN1bHRzW10+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHRzRGlyID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdzcmMnLCAnZGF0YScsICdyZXN1bHRzJyk7XG4gICAgXG4gICAgaWYgKCFmcy5leGlzdHNTeW5jKHJlc3VsdHNEaXIpKSB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IGZpbGVzID0gZnMucmVhZGRpclN5bmMocmVzdWx0c0RpcilcbiAgICAgIC5maWx0ZXIoZmlsZSA9PiBmaWxlLnN0YXJ0c1dpdGgoYCR7c3ltYm9sfV9gKSAmJiBmaWxlLmVuZHNXaXRoKCcuanNvbicpKVxuICAgICAgLnNvcnQoKVxuICAgICAgLnJldmVyc2UoKTsgLy8gTW9zdCByZWNlbnQgZmlyc3RcbiAgICBcbiAgICBjb25zdCByZXN1bHRzOiBBbmFseXNpc1Jlc3VsdHNbXSA9IFtdO1xuICAgIFxuICAgIGZvciAoY29uc3QgZmlsZSBvZiBmaWxlcykge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZmlsZXBhdGggPSBwYXRoLmpvaW4ocmVzdWx0c0RpciwgZmlsZSk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBmcy5yZWFkRmlsZVN5bmMoZmlsZXBhdGgsICd1dGY4Jyk7XG4gICAgICAgIHJlc3VsdHMucHVzaChKU09OLnBhcnNlKGRhdGEpIGFzIEFuYWx5c2lzUmVzdWx0cyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBFcnJvciByZWFkaW5nIGZpbGUgJHtmaWxlfTpgLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiByZXN1bHRzO1xuICAgIFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgYWxsIGFuYWx5c2lzIHJlc3VsdHM6JywgZXJyb3IpO1xuICAgIHJldHVybiBbXTtcbiAgfVxufVxuXG4vKipcbiAqIERlbGV0ZSBvbGQgYW5hbHlzaXMgcmVzdWx0cywga2VlcGluZyBvbmx5IHRoZSBtb3N0IHJlY2VudCBOIGZpbGVzIHBlciBzeW1ib2xcbiAqIEBwYXJhbSBzeW1ib2wgLSBTdG9jayBzeW1ib2xcbiAqIEBwYXJhbSBrZWVwQ291bnQgLSBOdW1iZXIgb2YgcmVjZW50IGZpbGVzIHRvIGtlZXAgKGRlZmF1bHQ6IDUpXG4gKiBAcmV0dXJucyBQcm9taXNlPG51bWJlcj4gLSBOdW1iZXIgb2YgZmlsZXMgZGVsZXRlZFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2xlYW51cE9sZFJlc3VsdHMoc3ltYm9sOiBzdHJpbmcsIGtlZXBDb3VudDogbnVtYmVyID0gNSk6IFByb21pc2U8bnVtYmVyPiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0c0RpciA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCAnc3JjJywgJ2RhdGEnLCAncmVzdWx0cycpO1xuICAgIFxuICAgIGlmICghZnMuZXhpc3RzU3luYyhyZXN1bHRzRGlyKSkge1xuICAgICAgcmV0dXJuIDA7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IGZpbGVzID0gZnMucmVhZGRpclN5bmMocmVzdWx0c0RpcilcbiAgICAgIC5maWx0ZXIoZmlsZSA9PiBmaWxlLnN0YXJ0c1dpdGgoYCR7c3ltYm9sfV9gKSAmJiBmaWxlLmVuZHNXaXRoKCcuanNvbicpKVxuICAgICAgLnNvcnQoKVxuICAgICAgLnJldmVyc2UoKTsgLy8gTW9zdCByZWNlbnQgZmlyc3RcbiAgICBcbiAgICBjb25zdCBmaWxlc1RvRGVsZXRlID0gZmlsZXMuc2xpY2Uoa2VlcENvdW50KTtcbiAgICBsZXQgZGVsZXRlZENvdW50ID0gMDtcbiAgICBcbiAgICBmb3IgKGNvbnN0IGZpbGUgb2YgZmlsZXNUb0RlbGV0ZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgZmlsZXBhdGggPSBwYXRoLmpvaW4ocmVzdWx0c0RpciwgZmlsZSk7XG4gICAgICAgIGZzLnVubGlua1N5bmMoZmlsZXBhdGgpO1xuICAgICAgICBkZWxldGVkQ291bnQrKztcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIGRlbGV0aW5nIGZpbGUgJHtmaWxlfTpgLCBlcnJvcik7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBkZWxldGVkQ291bnQ7XG4gICAgXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY2xlYW5pbmcgdXAgb2xkIHJlc3VsdHM6JywgZXJyb3IpO1xuICAgIHJldHVybiAwO1xuICB9XG59XG4iXSwibmFtZXMiOlsiZnMiLCJwYXRoIiwiZ2V0Q29tcGFueU5hbWUiLCJzYXZlQW5hbHlzaXNSZXN1bHRzIiwicmVzdWx0cyIsInJlc3VsdHNEaXIiLCJqb2luIiwicHJvY2VzcyIsImN3ZCIsImV4aXN0c1N5bmMiLCJta2RpclN5bmMiLCJyZWN1cnNpdmUiLCJ0aW1lc3RhbXAiLCJEYXRlIiwidG9JU09TdHJpbmciLCJyZXBsYWNlIiwic2xpY2UiLCJmaWxlbmFtZSIsInN5bWJvbCIsImZpbGVwYXRoIiwiZW5yaWNoZWRSZXN1bHRzIiwiY29tcGFueU5hbWUiLCJhbmFseXNpc0RhdGUiLCJzcGxpdCIsIndyaXRlRmlsZVN5bmMiLCJKU09OIiwic3RyaW5naWZ5IiwiY29uc29sZSIsImxvZyIsImVycm9yIiwibG9hZEFuYWx5c2lzUmVzdWx0cyIsImZpbGVzIiwicmVhZGRpclN5bmMiLCJmaWx0ZXIiLCJmaWxlIiwic3RhcnRzV2l0aCIsImVuZHNXaXRoIiwic29ydCIsInJldmVyc2UiLCJsZW5ndGgiLCJ0YXJnZXRGaWxlIiwiZmluZCIsImluY2x1ZGVzIiwiZGF0YSIsInJlYWRGaWxlU3luYyIsInBhcnNlIiwiZ2V0QWxsQW5hbHlzaXNSZXN1bHRzIiwicHVzaCIsImNsZWFudXBPbGRSZXN1bHRzIiwia2VlcENvdW50IiwiZmlsZXNUb0RlbGV0ZSIsImRlbGV0ZWRDb3VudCIsInVubGlua1N5bmMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/utils/resultsStorage.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsave_analysis_results&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csave_analysis_results.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();