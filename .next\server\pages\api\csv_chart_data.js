"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/csv_chart_data";
exports.ids = ["pages/api/csv_chart_data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\csv_chart_data.ts */ \"(api)/./src/pages/api/csv_chart_data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/csv_chart_data\",\n        pathname: \"/api/csv_chart_data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_csv_chart_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmNzdl9jaGFydF9kYXRhJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNhcGklNUNjc3ZfY2hhcnRfZGF0YS50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDRTtBQUMxRDtBQUNpRTtBQUNqRTtBQUNBLGlFQUFlLHdFQUFLLENBQUMsNkRBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLDZEQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLHlHQUFtQjtBQUNsRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vc3JjXFxcXHBhZ2VzXFxcXGFwaVxcXFxjc3ZfY2hhcnRfZGF0YS50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCAnZGVmYXVsdCcpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgJ2NvbmZpZycpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvY3N2X2NoYXJ0X2RhdGFcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jc3ZfY2hhcnRfZGF0YVwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/csv_chart_data.ts":
/*!*****************************************!*\
  !*** ./src/pages/api/csv_chart_data.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// src/pages/api/csv_chart_data.ts\n\n\nasync function handler(req, res) {\n    const { symbol } = req.query;\n    if (!symbol || Array.isArray(symbol)) {\n        return res.status(400).json({\n            error: 'symbol 파라미터가 필요합니다.'\n        });\n    }\n    try {\n        // CSV 파일 경로\n        const csvPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'data', 'sp500_adj_close_3y.csv');\n        // CSV 파일 읽기\n        const csvContent = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(csvPath, 'utf-8');\n        const lines = csvContent.split('\\n');\n        if (lines.length < 2) {\n            return res.status(500).json({\n                error: 'CSV 파일이 비어있습니다.'\n            });\n        }\n        // 헤더 파싱 (첫 번째 줄)\n        const headers = lines[0].split(',');\n        const symbolIndex = headers.findIndex((header)=>header.trim() === symbol.toUpperCase());\n        if (symbolIndex === -1) {\n            return res.status(404).json({\n                error: `심볼 ${symbol}을 찾을 수 없습니다.`\n            });\n        }\n        // 데이터 파싱\n        const chartData = [];\n        for(let i = 1; i < lines.length; i++){\n            const line = lines[i].trim();\n            if (!line) continue;\n            const values = line.split(',');\n            const date = values[0];\n            const priceStr = values[symbolIndex];\n            if (date && priceStr && priceStr !== '') {\n                const price = parseFloat(priceStr);\n                if (!isNaN(price) && price > 0) {\n                    chartData.push({\n                        time: date,\n                        value: price\n                    });\n                }\n            }\n        }\n        if (chartData.length === 0) {\n            return res.status(404).json({\n                error: `${symbol}에 대한 유효한 데이터가 없습니다.`\n            });\n        }\n        // 날짜순 정렬 (오래된 것부터)\n        chartData.sort((a, b)=>new Date(a.time).getTime() - new Date(b.time).getTime());\n        return res.status(200).json({\n            data: chartData,\n            symbol: symbol.toUpperCase()\n        });\n    } catch (error) {\n        console.error('CSV chart data error:', error);\n        return res.status(500).json({\n            error: '차트 데이터를 불러오는 중 오류가 발생했습니다.'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/csv_chart_data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcsv_chart_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Ccsv_chart_data.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();