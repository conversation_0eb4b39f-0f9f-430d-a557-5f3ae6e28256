"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fancy-canvas";
exports.ids = ["vendor-chunks/fancy-canvas"];
exports.modules = {

/***/ "(ssr)/./node_modules/fancy-canvas/canvas-element-bitmap-size.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/fancy-canvas/canvas-element-bitmap-size.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bindTo: () => (/* binding */ bindTo)\n/* harmony export */ });\n/* harmony import */ var _size_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./size.mjs */ \"(ssr)/./node_modules/fancy-canvas/size.mjs\");\n/* harmony import */ var _device_pixel_ratio_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./device-pixel-ratio.mjs */ \"(ssr)/./node_modules/fancy-canvas/device-pixel-ratio.mjs\");\n\n\nvar DevicePixelContentBoxBinding = /** @class */ (function () {\n    function DevicePixelContentBoxBinding(canvasElement, transformBitmapSize, options) {\n        var _a;\n        this._canvasElement = null;\n        this._bitmapSizeChangedListeners = [];\n        this._suggestedBitmapSize = null;\n        this._suggestedBitmapSizeChangedListeners = [];\n        // devicePixelRatio approach\n        this._devicePixelRatioObservable = null;\n        // ResizeObserver approach\n        this._canvasElementResizeObserver = null;\n        this._canvasElement = canvasElement;\n        this._canvasElementClientSize = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)({\n            width: this._canvasElement.clientWidth,\n            height: this._canvasElement.clientHeight,\n        });\n        this._transformBitmapSize = transformBitmapSize !== null && transformBitmapSize !== void 0 ? transformBitmapSize : (function (size) { return size; });\n        this._allowResizeObserver = (_a = options === null || options === void 0 ? void 0 : options.allowResizeObserver) !== null && _a !== void 0 ? _a : true;\n        this._chooseAndInitObserver();\n        // we MAY leave the constuctor without any bitmap size observation mechanics initialized\n    }\n    DevicePixelContentBoxBinding.prototype.dispose = function () {\n        var _a, _b;\n        if (this._canvasElement === null) {\n            throw new Error('Object is disposed');\n        }\n        (_a = this._canvasElementResizeObserver) === null || _a === void 0 ? void 0 : _a.disconnect();\n        this._canvasElementResizeObserver = null;\n        (_b = this._devicePixelRatioObservable) === null || _b === void 0 ? void 0 : _b.dispose();\n        this._devicePixelRatioObservable = null;\n        this._suggestedBitmapSizeChangedListeners.length = 0;\n        this._bitmapSizeChangedListeners.length = 0;\n        this._canvasElement = null;\n    };\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"canvasElement\", {\n        get: function () {\n            if (this._canvasElement === null) {\n                throw new Error('Object is disposed');\n            }\n            return this._canvasElement;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"canvasElementClientSize\", {\n        get: function () {\n            return this._canvasElementClientSize;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"bitmapSize\", {\n        get: function () {\n            return (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)({\n                width: this.canvasElement.width,\n                height: this.canvasElement.height,\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Use this function to change canvas element client size until binding is disposed\n     * @param clientSize New client size for bound HTMLCanvasElement\n     */\n    DevicePixelContentBoxBinding.prototype.resizeCanvasElement = function (clientSize) {\n        this._canvasElementClientSize = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)(clientSize);\n        this.canvasElement.style.width = \"\".concat(this._canvasElementClientSize.width, \"px\");\n        this.canvasElement.style.height = \"\".concat(this._canvasElementClientSize.height, \"px\");\n        this._invalidateBitmapSize();\n    };\n    DevicePixelContentBoxBinding.prototype.subscribeBitmapSizeChanged = function (listener) {\n        this._bitmapSizeChangedListeners.push(listener);\n    };\n    DevicePixelContentBoxBinding.prototype.unsubscribeBitmapSizeChanged = function (listener) {\n        this._bitmapSizeChangedListeners = this._bitmapSizeChangedListeners.filter(function (l) { return l !== listener; });\n    };\n    Object.defineProperty(DevicePixelContentBoxBinding.prototype, \"suggestedBitmapSize\", {\n        get: function () {\n            return this._suggestedBitmapSize;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    DevicePixelContentBoxBinding.prototype.subscribeSuggestedBitmapSizeChanged = function (listener) {\n        this._suggestedBitmapSizeChangedListeners.push(listener);\n    };\n    DevicePixelContentBoxBinding.prototype.unsubscribeSuggestedBitmapSizeChanged = function (listener) {\n        this._suggestedBitmapSizeChangedListeners = this._suggestedBitmapSizeChangedListeners.filter(function (l) { return l !== listener; });\n    };\n    DevicePixelContentBoxBinding.prototype.applySuggestedBitmapSize = function () {\n        if (this._suggestedBitmapSize === null) {\n            // nothing to apply\n            return;\n        }\n        var oldSuggestedSize = this._suggestedBitmapSize;\n        this._suggestedBitmapSize = null;\n        this._resizeBitmap(oldSuggestedSize);\n        this._emitSuggestedBitmapSizeChanged(oldSuggestedSize, this._suggestedBitmapSize);\n    };\n    DevicePixelContentBoxBinding.prototype._resizeBitmap = function (newSize) {\n        var oldSize = this.bitmapSize;\n        if ((0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.equalSizes)(oldSize, newSize)) {\n            return;\n        }\n        this.canvasElement.width = newSize.width;\n        this.canvasElement.height = newSize.height;\n        this._emitBitmapSizeChanged(oldSize, newSize);\n    };\n    DevicePixelContentBoxBinding.prototype._emitBitmapSizeChanged = function (oldSize, newSize) {\n        var _this = this;\n        this._bitmapSizeChangedListeners.forEach(function (listener) { return listener.call(_this, oldSize, newSize); });\n    };\n    DevicePixelContentBoxBinding.prototype._suggestNewBitmapSize = function (newSize) {\n        var oldSuggestedSize = this._suggestedBitmapSize;\n        var finalNewSize = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)(this._transformBitmapSize(newSize, this._canvasElementClientSize));\n        var newSuggestedSize = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.equalSizes)(this.bitmapSize, finalNewSize) ? null : finalNewSize;\n        if (oldSuggestedSize === null && newSuggestedSize === null) {\n            return;\n        }\n        if (oldSuggestedSize !== null && newSuggestedSize !== null\n            && (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.equalSizes)(oldSuggestedSize, newSuggestedSize)) {\n            return;\n        }\n        this._suggestedBitmapSize = newSuggestedSize;\n        this._emitSuggestedBitmapSizeChanged(oldSuggestedSize, newSuggestedSize);\n    };\n    DevicePixelContentBoxBinding.prototype._emitSuggestedBitmapSizeChanged = function (oldSize, newSize) {\n        var _this = this;\n        this._suggestedBitmapSizeChangedListeners.forEach(function (listener) { return listener.call(_this, oldSize, newSize); });\n    };\n    DevicePixelContentBoxBinding.prototype._chooseAndInitObserver = function () {\n        var _this = this;\n        if (!this._allowResizeObserver) {\n            this._initDevicePixelRatioObservable();\n            return;\n        }\n        isDevicePixelContentBoxSupported()\n            .then(function (isSupported) {\n            return isSupported ?\n                _this._initResizeObserver() :\n                _this._initDevicePixelRatioObservable();\n        });\n    };\n    // devicePixelRatio approach\n    DevicePixelContentBoxBinding.prototype._initDevicePixelRatioObservable = function () {\n        var _this = this;\n        if (this._canvasElement === null) {\n            // it looks like we are already dead\n            return;\n        }\n        var win = canvasElementWindow(this._canvasElement);\n        if (win === null) {\n            throw new Error('No window is associated with the canvas');\n        }\n        this._devicePixelRatioObservable = (0,_device_pixel_ratio_mjs__WEBPACK_IMPORTED_MODULE_1__.createObservable)(win);\n        this._devicePixelRatioObservable.subscribe(function () { return _this._invalidateBitmapSize(); });\n        this._invalidateBitmapSize();\n    };\n    DevicePixelContentBoxBinding.prototype._invalidateBitmapSize = function () {\n        var _a, _b;\n        if (this._canvasElement === null) {\n            // it looks like we are already dead\n            return;\n        }\n        var win = canvasElementWindow(this._canvasElement);\n        if (win === null) {\n            return;\n        }\n        var ratio = (_b = (_a = this._devicePixelRatioObservable) === null || _a === void 0 ? void 0 : _a.value) !== null && _b !== void 0 ? _b : win.devicePixelRatio;\n        var canvasRects = this._canvasElement.getClientRects();\n        var newSize = \n        // eslint-disable-next-line no-negated-condition\n        canvasRects[0] !== undefined ?\n            predictedBitmapSize(canvasRects[0], ratio) :\n            (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)({\n                width: this._canvasElementClientSize.width * ratio,\n                height: this._canvasElementClientSize.height * ratio,\n            });\n        this._suggestNewBitmapSize(newSize);\n    };\n    // ResizeObserver approach\n    DevicePixelContentBoxBinding.prototype._initResizeObserver = function () {\n        var _this = this;\n        if (this._canvasElement === null) {\n            // it looks like we are already dead\n            return;\n        }\n        this._canvasElementResizeObserver = new ResizeObserver(function (entries) {\n            var entry = entries.find(function (entry) { return entry.target === _this._canvasElement; });\n            if (!entry || !entry.devicePixelContentBoxSize || !entry.devicePixelContentBoxSize[0]) {\n                return;\n            }\n            var entrySize = entry.devicePixelContentBoxSize[0];\n            var newSize = (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)({\n                width: entrySize.inlineSize,\n                height: entrySize.blockSize,\n            });\n            _this._suggestNewBitmapSize(newSize);\n        });\n        this._canvasElementResizeObserver.observe(this._canvasElement, { box: 'device-pixel-content-box' });\n    };\n    return DevicePixelContentBoxBinding;\n}());\nfunction bindTo(canvasElement, target) {\n    if (target.type === 'device-pixel-content-box') {\n        return new DevicePixelContentBoxBinding(canvasElement, target.transform, target.options);\n    }\n    throw new Error('Unsupported binding target');\n}\nfunction canvasElementWindow(canvasElement) {\n    // According to DOM Level 2 Core specification, ownerDocument should never be null for HTMLCanvasElement\n    // see https://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#node-ownerDoc\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    return canvasElement.ownerDocument.defaultView;\n}\nfunction isDevicePixelContentBoxSupported() {\n    return new Promise(function (resolve) {\n        var ro = new ResizeObserver(function (entries) {\n            resolve(entries.every(function (entry) { return 'devicePixelContentBoxSize' in entry; }));\n            ro.disconnect();\n        });\n        ro.observe(document.body, { box: 'device-pixel-content-box' });\n    })\n        .catch(function () { return false; });\n}\nfunction predictedBitmapSize(canvasRect, ratio) {\n    return (0,_size_mjs__WEBPACK_IMPORTED_MODULE_0__.size)({\n        width: Math.round(canvasRect.left * ratio + canvasRect.width * ratio) -\n            Math.round(canvasRect.left * ratio),\n        height: Math.round(canvasRect.top * ratio + canvasRect.height * ratio) -\n            Math.round(canvasRect.top * ratio),\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fancy-canvas/canvas-element-bitmap-size.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/fancy-canvas/canvas-rendering-target.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/fancy-canvas/canvas-rendering-target.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CanvasRenderingTarget2D: () => (/* binding */ CanvasRenderingTarget2D),\n/* harmony export */   createCanvasRenderingTarget2D: () => (/* binding */ createCanvasRenderingTarget2D),\n/* harmony export */   tryCreateCanvasRenderingTarget2D: () => (/* binding */ tryCreateCanvasRenderingTarget2D)\n/* harmony export */ });\n/**\n * @experimental\n */\nvar CanvasRenderingTarget2D = /** @class */ (function () {\n    function CanvasRenderingTarget2D(context, mediaSize, bitmapSize) {\n        if (mediaSize.width === 0 || mediaSize.height === 0) {\n            throw new TypeError('Rendering target could only be created on a media with positive width and height');\n        }\n        this._mediaSize = mediaSize;\n        // !Number.isInteger(bitmapSize.width) || !Number.isInteger(bitmapSize.height)\n        if (bitmapSize.width === 0 || bitmapSize.height === 0) {\n            throw new TypeError('Rendering target could only be created using a bitmap with positive integer width and height');\n        }\n        this._bitmapSize = bitmapSize;\n        this._context = context;\n    }\n    CanvasRenderingTarget2D.prototype.useMediaCoordinateSpace = function (f) {\n        try {\n            this._context.save();\n            // do not use resetTransform to support old versions of Edge\n            this._context.setTransform(1, 0, 0, 1, 0, 0);\n            this._context.scale(this._horizontalPixelRatio, this._verticalPixelRatio);\n            return f({\n                context: this._context,\n                mediaSize: this._mediaSize,\n            });\n        }\n        finally {\n            this._context.restore();\n        }\n    };\n    CanvasRenderingTarget2D.prototype.useBitmapCoordinateSpace = function (f) {\n        try {\n            this._context.save();\n            // do not use resetTransform to support old versions of Edge\n            this._context.setTransform(1, 0, 0, 1, 0, 0);\n            return f({\n                context: this._context,\n                mediaSize: this._mediaSize,\n                bitmapSize: this._bitmapSize,\n                horizontalPixelRatio: this._horizontalPixelRatio,\n                verticalPixelRatio: this._verticalPixelRatio,\n            });\n        }\n        finally {\n            this._context.restore();\n        }\n    };\n    Object.defineProperty(CanvasRenderingTarget2D.prototype, \"_horizontalPixelRatio\", {\n        get: function () {\n            return this._bitmapSize.width / this._mediaSize.width;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CanvasRenderingTarget2D.prototype, \"_verticalPixelRatio\", {\n        get: function () {\n            return this._bitmapSize.height / this._mediaSize.height;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CanvasRenderingTarget2D;\n}());\n\n/**\n * @experimental\n */\nfunction createCanvasRenderingTarget2D(binding, contextOptions) {\n    var mediaSize = binding.canvasElementClientSize;\n    var bitmapSize = binding.bitmapSize;\n    var context = binding.canvasElement.getContext('2d', contextOptions);\n    if (context === null) {\n        throw new Error('Could not get 2d drawing context from bound canvas element. Has the canvas already been set to a different context mode?');\n    }\n    return new CanvasRenderingTarget2D(context, mediaSize, bitmapSize);\n}\n/**\n * @experimental\n */\nfunction tryCreateCanvasRenderingTarget2D(binding, contextOptions) {\n    var mediaSize = binding.canvasElementClientSize;\n    if (mediaSize.width === 0 || mediaSize.height === 0) {\n        return null;\n    }\n    var bitmapSize = binding.bitmapSize;\n    if (bitmapSize.width === 0 || bitmapSize.height === 0) {\n        return null;\n    }\n    var context = binding.canvasElement.getContext('2d', contextOptions);\n    if (context === null) {\n        return null;\n    }\n    return new CanvasRenderingTarget2D(context, mediaSize, bitmapSize);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fancy-canvas/canvas-rendering-target.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/fancy-canvas/device-pixel-ratio.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/fancy-canvas/device-pixel-ratio.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createObservable: () => (/* binding */ createObservable)\n/* harmony export */ });\nvar Observable = /** @class */ (function () {\n    function Observable(win) {\n        var _this = this;\n        this._resolutionListener = function () { return _this._onResolutionChanged(); };\n        this._resolutionMediaQueryList = null;\n        this._observers = [];\n        this._window = win;\n        this._installResolutionListener();\n    }\n    Observable.prototype.dispose = function () {\n        this._uninstallResolutionListener();\n        this._window = null;\n    };\n    Object.defineProperty(Observable.prototype, \"value\", {\n        get: function () {\n            return this._window.devicePixelRatio;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Observable.prototype.subscribe = function (next) {\n        var _this = this;\n        var observer = { next: next };\n        this._observers.push(observer);\n        return {\n            unsubscribe: function () {\n                _this._observers = _this._observers.filter(function (o) { return o !== observer; });\n            },\n        };\n    };\n    Observable.prototype._installResolutionListener = function () {\n        if (this._resolutionMediaQueryList !== null) {\n            throw new Error('Resolution listener is already installed');\n        }\n        var dppx = this._window.devicePixelRatio;\n        this._resolutionMediaQueryList = this._window.matchMedia(\"all and (resolution: \".concat(dppx, \"dppx)\"));\n        // IE and some versions of Edge do not support addEventListener/removeEventListener, and we are going to use the deprecated addListener/removeListener\n        this._resolutionMediaQueryList.addListener(this._resolutionListener);\n    };\n    Observable.prototype._uninstallResolutionListener = function () {\n        if (this._resolutionMediaQueryList !== null) {\n            // IE and some versions of Edge do not support addEventListener/removeEventListener, and we are going to use the deprecated addListener/removeListener\n            this._resolutionMediaQueryList.removeListener(this._resolutionListener);\n            this._resolutionMediaQueryList = null;\n        }\n    };\n    Observable.prototype._reinstallResolutionListener = function () {\n        this._uninstallResolutionListener();\n        this._installResolutionListener();\n    };\n    Observable.prototype._onResolutionChanged = function () {\n        var _this = this;\n        this._observers.forEach(function (observer) { return observer.next(_this._window.devicePixelRatio); });\n        this._reinstallResolutionListener();\n    };\n    return Observable;\n}());\nfunction createObservable(win) {\n    return new Observable(win);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fancy-canvas/device-pixel-ratio.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/fancy-canvas/index.mjs":
/*!*********************************************!*\
  !*** ./node_modules/fancy-canvas/index.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CanvasRenderingTarget2D: () => (/* reexport safe */ _canvas_rendering_target_mjs__WEBPACK_IMPORTED_MODULE_2__.CanvasRenderingTarget2D),\n/* harmony export */   bindCanvasElementBitmapSizeTo: () => (/* reexport safe */ _canvas_element_bitmap_size_mjs__WEBPACK_IMPORTED_MODULE_1__.bindTo),\n/* harmony export */   createCanvasRenderingTarget2D: () => (/* reexport safe */ _canvas_rendering_target_mjs__WEBPACK_IMPORTED_MODULE_2__.createCanvasRenderingTarget2D),\n/* harmony export */   equalSizes: () => (/* reexport safe */ _size_mjs__WEBPACK_IMPORTED_MODULE_0__.equalSizes),\n/* harmony export */   size: () => (/* reexport safe */ _size_mjs__WEBPACK_IMPORTED_MODULE_0__.size),\n/* harmony export */   tryCreateCanvasRenderingTarget2D: () => (/* reexport safe */ _canvas_rendering_target_mjs__WEBPACK_IMPORTED_MODULE_2__.tryCreateCanvasRenderingTarget2D)\n/* harmony export */ });\n/* harmony import */ var _size_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./size.mjs */ \"(ssr)/./node_modules/fancy-canvas/size.mjs\");\n/* harmony import */ var _canvas_element_bitmap_size_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./canvas-element-bitmap-size.mjs */ \"(ssr)/./node_modules/fancy-canvas/canvas-element-bitmap-size.mjs\");\n/* harmony import */ var _canvas_rendering_target_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./canvas-rendering-target.mjs */ \"(ssr)/./node_modules/fancy-canvas/canvas-rendering-target.mjs\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFuY3ktY2FudmFzL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBOEM7QUFDOEM7QUFDOEMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29uZzdcXERlc2t0b3BcXGhvbWVcXHVidW50dVxcZmluYW5jaWFsX2Rhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxmYW5jeS1jYW52YXNcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBzaXplLCBlcXVhbFNpemVzIH0gZnJvbSBcIi4vc2l6ZS5tanNcIjtcbmV4cG9ydCB7IGJpbmRUbyBhcyBiaW5kQ2FudmFzRWxlbWVudEJpdG1hcFNpemVUbywgfSBmcm9tIFwiLi9jYW52YXMtZWxlbWVudC1iaXRtYXAtc2l6ZS5tanNcIjtcbmV4cG9ydCB7IENhbnZhc1JlbmRlcmluZ1RhcmdldDJELCBjcmVhdGVDYW52YXNSZW5kZXJpbmdUYXJnZXQyRCwgdHJ5Q3JlYXRlQ2FudmFzUmVuZGVyaW5nVGFyZ2V0MkQsIH0gZnJvbSBcIi4vY2FudmFzLXJlbmRlcmluZy10YXJnZXQubWpzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fancy-canvas/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/fancy-canvas/size.mjs":
/*!********************************************!*\
  !*** ./node_modules/fancy-canvas/size.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   equalSizes: () => (/* binding */ equalSizes),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\nfunction size(_a) {\n    var width = _a.width, height = _a.height;\n    if (width < 0) {\n        throw new Error('Negative width is not allowed for Size');\n    }\n    if (height < 0) {\n        throw new Error('Negative height is not allowed for Size');\n    }\n    return {\n        width: width,\n        height: height,\n    };\n}\nfunction equalSizes(first, second) {\n    return (first.width === second.width) &&\n        (first.height === second.height);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmFuY3ktY2FudmFzL3NpemUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxzb25nN1xcRGVza3RvcFxcaG9tZVxcdWJ1bnR1XFxmaW5hbmNpYWxfZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGZhbmN5LWNhbnZhc1xcc2l6ZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIHNpemUoX2EpIHtcbiAgICB2YXIgd2lkdGggPSBfYS53aWR0aCwgaGVpZ2h0ID0gX2EuaGVpZ2h0O1xuICAgIGlmICh3aWR0aCA8IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdOZWdhdGl2ZSB3aWR0aCBpcyBub3QgYWxsb3dlZCBmb3IgU2l6ZScpO1xuICAgIH1cbiAgICBpZiAoaGVpZ2h0IDwgMCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ05lZ2F0aXZlIGhlaWdodCBpcyBub3QgYWxsb3dlZCBmb3IgU2l6ZScpO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICB3aWR0aDogd2lkdGgsXG4gICAgICAgIGhlaWdodDogaGVpZ2h0LFxuICAgIH07XG59XG5leHBvcnQgZnVuY3Rpb24gZXF1YWxTaXplcyhmaXJzdCwgc2Vjb25kKSB7XG4gICAgcmV0dXJuIChmaXJzdC53aWR0aCA9PT0gc2Vjb25kLndpZHRoKSAmJlxuICAgICAgICAoZmlyc3QuaGVpZ2h0ID09PSBzZWNvbmQuaGVpZ2h0KTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fancy-canvas/size.mjs\n");

/***/ })

};
;