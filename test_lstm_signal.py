#!/usr/bin/env python3
"""
Test script for LSTM signal light implementation
"""

import sys
import os
from pathlib import Path

# Add the services directory to the path
sys.path.append(str(Path(__file__).parent / "src" / "services"))

try:
    from lstm_finetuning import process_single_ticker
    from datetime import date
    
    print("Testing LSTM signal light implementation...")
    
    # Test with a simple case
    ticker = "AAPL"
    target_date = date(2024, 12, 15)
    
    print(f"Processing {ticker} for {target_date}")
    
    result = process_single_ticker(ticker, target_date, use_volume=True, save_to_file=False)
    
    if result:
        print("✅ Success!")
        print(f"Signal: {result.get('signal_colour')} ({result.get('confidence_tag')})")
        print(f"Hits: {result.get('hits_last5')}/5")
        print(f"Metrics: {result.get('metrics')}")
        if result.get('signal_disabled'):
            print("⚠️ Signal disabled due to low accuracy")
    else:
        print("❌ Failed to process")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
