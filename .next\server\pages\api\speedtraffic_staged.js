"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/speedtraffic_staged";
exports.ids = ["pages/api/speedtraffic_staged"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeedtraffic_staged&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeedtraffic_staged.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeedtraffic_staged&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeedtraffic_staged.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_speedtraffic_staged_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\speedtraffic_staged.ts */ \"(api)/./src/pages/api/speedtraffic_staged.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_speedtraffic_staged_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_speedtraffic_staged_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/speedtraffic_staged\",\n        pathname: \"/api/speedtraffic_staged\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_speedtraffic_staged_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeedtraffic_staged&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeedtraffic_staged.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/speedtraffic_staged.ts":
/*!**********************************************!*\
  !*** ./src/pages/api/speedtraffic_staged.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n// Staged execution API for SpeedTraffic component\n// Phase 1: Fast services (Technical, Industry, Market, Volatility)\n// Phase 2: LSTM service (Neural Network Prediction)\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol, stage } = req.query;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    if (!stage || stage !== 'phase1' && stage !== 'phase2') {\n        return res.status(400).json({\n            error: 'Stage must be either \"phase1\" or \"phase2\"'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    try {\n        // Delegate to the main API with stage parameter\n        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';\n        const response = await fetch(`${baseUrl}/api/lstm_prediction_simple?symbol=${ticker}&stage=${stage}`, {\n            method: 'GET',\n            headers: {\n                'Accept': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const result = await response.json();\n        res.status(200).json(result);\n    } catch (error) {\n        console.error(`[SPEEDTRAFFIC_STAGED] Error for ${ticker} stage ${stage}:`, error);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Staged prediction failed',\n            message: errorMessage,\n            stage: stage,\n            timestamp: new Date().toISOString()\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/speedtraffic_staged.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fspeedtraffic_staged&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cspeedtraffic_staged.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();