#!/usr/bin/env python3
"""
Test script to verify LSTM and MFI services work correctly
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def test_service(script_name, ticker):
    """Test a Python service script"""
    script_path = Path(__file__).parent / "src" / "services" / script_name
    
    print(f"\n=== Testing {script_name} with {ticker} ===")
    print(f"Script path: {script_path}")
    print(f"Script exists: {script_path.exists()}")
    
    if not script_path.exists():
        print(f"ERROR: Script {script_path} not found!")
        return False
    
    try:
        # Run the script
        result = subprocess.run(
            [sys.executable, str(script_path), ticker],
            capture_output=True,
            text=True,
            timeout=120,
            cwd=script_path.parent
        )
        
        print(f"Return code: {result.returncode}")
        print(f"Stdout length: {len(result.stdout)}")
        print(f"Stderr length: {len(result.stderr)}")
        
        if result.stdout:
            print("=== STDOUT ===")
            print(result.stdout)
        
        if result.stderr:
            print("=== STDERR ===")
            print(result.stderr)
        
        if result.returncode == 0:
            try:
                # Try to parse JSON output
                json_output = json.loads(result.stdout.strip())
                print("✅ JSON output parsed successfully")
                print(f"JSON keys: {list(json_output.keys())}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON parse error: {e}")
                return False
        else:
            print(f"❌ Process failed with code {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Process timed out")
        return False
    except Exception as e:
        print(f"❌ Error running script: {e}")
        return False

def main():
    """Main test function"""
    ticker = "INTC"
    
    print(f"Testing Python services with ticker: {ticker}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python executable: {sys.executable}")
    
    # Test LSTM service
    lstm_success = test_service("lstm_service.py", ticker)
    
    # Test MFI service
    mfi_success = test_service("mfi_service.py", ticker)
    
    print(f"\n=== SUMMARY ===")
    print(f"LSTM service: {'✅ PASS' if lstm_success else '❌ FAIL'}")
    print(f"MFI service: {'✅ PASS' if mfi_success else '❌ FAIL'}")
    
    if lstm_success and mfi_success:
        print("🎉 All services working correctly!")
        return 0
    else:
        print("💥 Some services failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
