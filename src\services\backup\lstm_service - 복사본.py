#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service
6-feature input (adj close, volume, RSI-14, Bo<PERSON>er %) with 75-day window / 5-day horizon
Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1) with bias offset only
"""

import os
import sys
import json
from datetime import date, timedelta
from pathlib import Path

# Module-level constants - June 5, 2025 as day 0 (reference date)
REFERENCE_DATE = date(2025, 6, 5)  # Day 0 - consistent with other Python services

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Enable oneDNN verbose if debug flag is set
if os.environ.get('LSTM_DEBUG_ONEDNN') == '1':
    os.environ['ONEDNN_VERBOSE'] = '1'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# Import required libraries
import pandas as pd
import numpy as np
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib
import tensorflow as tf
from tensorflow.keras import Sequential
from tensorflow.keras.layers import Dense, LSTM, Dropout
from tensorflow.keras.callbacks import EarlyStopping

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier

# Intel optimizations enabled via environment variables

# Configure TensorFlow threading for Intel Core Ultra 7 155H
tf.config.threading.set_intra_op_parallelism_threads(16)
tf.config.threading.set_inter_op_parallelism_threads(2)
tf.get_logger().setLevel('ERROR')

# Print startup log
print(f"✅  TensorFlow-Intel {tf.__version__} — oneDNN enabled", file=sys.stderr)


def compute_indicators(df):
    """
    Compute technical indicators for LSTM features

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with columns ['Date', 'close', 'volume'] (or similar)

    Returns:
    --------
    pandas.DataFrame
        DataFrame with columns: ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    """
    # Ensure we have the required columns
    if 'Adj Close' in df.columns:
        df = df.rename(columns={'Adj Close': 'close'})
    if 'Volume' in df.columns:
        df = df.rename(columns={'Volume': 'volume'})

    # Sort by date to ensure proper calculation
    df = df.sort_values('Date').copy()

    # Calculate RSI(14)
    delta = df['close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)

    # Use exponential moving average for RSI calculation
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    # Normalize RSI from 0-100 range to 0-1 scale
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['close'].rolling(window=BB_PERIOD).mean()
    std = df['close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)

    # Calculate Bollinger Band features as percentages
    # bb_upper_pct: How far current price is above upper band (as percentage)
    df['bb_upper_pct'] = ((df['close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)

    # bb_lower_pct: How far current price is above lower band (as percentage)
    df['bb_lower_pct'] = ((df['close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)

    # bb_width_pct: Band width as percentage of middle line
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Remove NaN values and reset index to ensure proper alignment
    df_clean = df.dropna().reset_index(drop=True)

    # Debug logging for technical indicator statistics
    print(f"Technical indicator stats:", file=sys.stderr)
    print(f"  Original data rows: {len(df)}, After NaN removal: {len(df_clean)}", file=sys.stderr)
    print(f"  RSI14 range: {df_clean['rsi14'].min():.3f} - {df_clean['rsi14'].max():.3f}", file=sys.stderr)
    print(f"  BB Upper %: {df_clean['bb_upper_pct'].min():.1f} - {df_clean['bb_upper_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Lower %: {df_clean['bb_lower_pct'].min():.1f} - {df_clean['bb_lower_pct'].max():.1f}", file=sys.stderr)
    print(f"  BB Width %: {df_clean['bb_width_pct'].min():.1f} - {df_clean['bb_width_pct'].max():.1f}", file=sys.stderr)
    print(f"  Date range: {df_clean['Date'].min().date()} to {df_clean['Date'].max().date()}", file=sys.stderr)

    # Return DataFrame with Date column and exact feature column order required
    feature_cols = ['Date', 'close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    return df_clean[feature_cols]


# Removed update_history_csv function - no longer needed for traffic history tracking


def load_and_prepare_data(ticker, use_volume=False):
    """Load data and prepare for training with robust date handling for limited datasets"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]              # …/financial_dashboard
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)

        # Parse Date column - keep as Timestamp for consistent comparison
        df['Date'] = pd.to_datetime(df['Date'])

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Get available date range from dataset
        min_date = df['Date'].min()
        max_date = df['Date'].max()

        print(f"Dataset range: {min_date.date()} to {max_date.date()}", file=sys.stderr)

        # Calculate training end date (5 business days before reference date)
        reference_ts = pd.Timestamp(REFERENCE_DATE)
        train_end_date = reference_ts - BDay(5)  # 5 business days before reference date (day -5)

        # Calculate ideal training start (3.5 years before reference date)
        ideal_start = reference_ts - pd.Timedelta(days=int(3.5 * 365))

        # Use maximum available historical data (start from dataset beginning if needed)
        train_start_date = max(min_date, ideal_start)

        print(f"Training range: {train_start_date.date()} to {train_end_date.date()}", file=sys.stderr)

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if not volume_path.exists():
                print(f"Volume data file not found: {volume_path}", file=sys.stderr)
                sys.exit(1)

            volume_df = pd.read_csv(volume_path)
            volume_df['Date'] = pd.to_datetime(volume_df['Date'])  # Keep as Timestamp

            # Convert numeric columns to float64
            for col in volume_df.columns:
                if col != 'Date':
                    volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

            if ticker not in volume_df.columns:
                print(f"Ticker {ticker} not found in volume data", file=sys.stderr)
                sys.exit(1)

            volume_data = volume_df[['Date', ticker]].copy()
            volume_data.columns = ['Date', 'Volume']

        # Filter training data using available date range
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()

        # Check minimum rows requirement
        if len(train_data) < 100:
            print(f"Insufficient training data: {len(train_data)} rows (minimum 100 required)", file=sys.stderr)
            print(f"Available training samples: {len(train_data)}", file=sys.stderr)
            sys.exit(1)

        # Get all data for predictions
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) &
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date.date()
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date.date()

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(df, feature_cols=None):
    """
    Create sequences for LSTM training with technical indicators

    Parameters:
    -----------
    df : pandas.DataFrame
        DataFrame with technical indicator features
    feature_cols : list, optional
        List of feature column names. If None, uses all columns except 'Date'

    Returns:
    --------
    tuple
        (X, y) where X has shape (samples, WINDOW_DAYS, num_features) and y is binary labels
    """
    if feature_cols is None:
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']

    X, y = [], []

    # Iterate from 0 to (len(df) - WINDOW_DAYS - HORIZON_DAYS)
    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols] with shape (75, 6)
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values

        # Verify sequence shape
        if sequence.shape != (WINDOW_DAYS, len(feature_cols)):
            print(f"Warning: sequence shape {sequence.shape} != expected ({WINDOW_DAYS}, {len(feature_cols)})", file=sys.stderr)
            continue

        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if close[target_date] > close[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (close)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (close)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance more effectively than class weights"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def sigmoid(x):
    """Sigmoid function for temperature calibration (from fine-tuning implementation)"""
    return 1 / (1 + np.exp(-x))


def build_lstm_model(input_shape=(WINDOW_DAYS, 6)):
    """
    Build simplified LSTM classifier model

    Architecture: LSTM(32) → Dropout(0.2) → LSTM(32) → Dense(1, sigmoid)
    Loss: focal_loss(gamma=2.0) with no class_weight
    """
    model = Sequential([
        LSTM(32, return_sequences=True, input_shape=input_shape),
        Dropout(0.2),
        LSTM(32, return_sequences=False),
        Dense(1, activation='sigmoid')
    ])

    model.compile(
        optimizer='adam',
        loss=focal_loss(gamma=2.0),
        metrics=['binary_accuracy']
    )

    return model


def build_sequence(all_data, indicators_df, scaler, target_date):
    """
    Build a single sequence for prediction on target_date with proper data alignment

    Parameters:
    -----------
    all_data : pandas.DataFrame
        DataFrame with Date and price columns (Date as Timestamp)
    indicators_df : pandas.DataFrame
        DataFrame with technical indicators (must have Date column)
    scaler : sklearn.preprocessing.MinMaxScaler
        Fitted scaler for features
    target_date : date or Timestamp
        Target date for sequence building

    Returns:
    --------
    numpy.ndarray
        Scaled sequence of shape (1, 75, 6)
    """
    # Convert target_date to Timestamp for consistent comparison
    target_ts = pd.Timestamp(target_date)

    # Find target date in indicators data
    date_mask = indicators_df['Date'] == target_ts
    if not date_mask.any():
        # Find nearest available date before or on target
        available_dates = indicators_df['Date'].values
        dates_before = [d for d in available_dates if d <= target_ts]
        if not dates_before:
            raise ValueError(f"No data available before {target_date}")
        actual_date = max(dates_before)
        date_mask = indicators_df['Date'] == actual_date
        print(f"Using nearest date {actual_date.date()} for target {target_date}", file=sys.stderr)

    # Get the row number (not index) in the indicators DataFrame
    matching_rows = indicators_df[date_mask]
    if len(matching_rows) == 0:
        raise ValueError(f"No matching date found for {target_date}")

    # Use iloc position (row number) instead of index
    date_position = matching_rows.index[0]
    row_number = indicators_df.index.get_loc(date_position)

    print(f"Target date: {target_date}, Row position: {row_number}, Total rows: {len(indicators_df)}", file=sys.stderr)

    # Check if we have enough data for WINDOW_DAYS sequence
    if row_number < WINDOW_DAYS:
        raise ValueError(f"Insufficient data before {target_date}: need {WINDOW_DAYS} days, have {row_number}")

    # Extract most recent WINDOW_DAYS (75) days from indicators data using iloc
    feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
    start_row = row_number - WINDOW_DAYS
    end_row = row_number

    print(f"Extracting rows {start_row}:{end_row} for sequence", file=sys.stderr)
    sequence_data = indicators_df.iloc[start_row:end_row][feature_cols].values

    # Verify sequence shape before scaling
    if sequence_data.shape != (WINDOW_DAYS, 6):
        print(f"Available data shape: {sequence_data.shape}, expected: ({WINDOW_DAYS}, 6)", file=sys.stderr)
        raise ValueError(f"Sequence shape mismatch: expected ({WINDOW_DAYS}, 6), got {sequence_data.shape}")

    sequence_scaled = scaler.transform(sequence_data)
    return sequence_scaled.reshape(1, WINDOW_DAYS, 6)  # (1, 75, 6)


def get_actual_label_if_available(all_data, target_date):
    """
    Get actual label for target_date if data is available

    Returns None for future dates, 0/1 for historical dates
    """
    try:
        # Convert target_date to Timestamp for consistent comparison
        target_ts = pd.Timestamp(target_date)

        date_mask = all_data['Date'] == target_ts
        if not date_mask.any():
            return None

        date_idx = all_data[date_mask].index[0]

        # Check if we have future data for HORIZON_DAYS prediction
        if date_idx + HORIZON_DAYS - 1 >= len(all_data):
            return None

        # Calculate actual label
        price_col = 'Adj Close' if 'Adj Close' in all_data.columns else 'close'
        current_price = all_data.iloc[date_idx-1][price_col]
        future_price = all_data.iloc[date_idx + HORIZON_DAYS - 1][price_col]
        return 1 if future_price > current_price else 0

    except Exception:
        return None


def main():
    """
    Main function with new 6-day prediction approach

    Trains up to day -5, predicts 6 days (-4, -3, -2, -1, 0, +1)
    Uses bias offset only (no calibration/thresholds)
    """
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python lstm_service.py <TICKER> [--no-volume]", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    # Enable volume by default, disable with --no-volume flag
    use_volume = True
    if len(sys.argv) == 3 and sys.argv[2] == '--no-volume':
        use_volume = False

    try:
        # Load and prepare data with robust date handling
        all_data, train_data, _ = load_and_prepare_data(ticker, use_volume)

        # Calculate train_until (5 business days before reference date)
        train_until = pd.Timestamp(REFERENCE_DATE) - BDay(5)

        # Filter training data up to train_until
        train_data_filtered = train_data[train_data['Date'] <= train_until].copy()

        if len(train_data_filtered) < 100:
            print(f"Insufficient training data for {ticker}", file=sys.stderr)
            sys.exit(1)

        # Compute technical indicators on training data
        train_indicators = compute_indicators(train_data_filtered)

        print(f"학습 데이터 형태: {train_indicators.shape}", file=sys.stderr)

        # Scale the features (exclude Date column)
        feature_cols = ['close', 'volume', 'rsi14', 'bb_upper_pct', 'bb_lower_pct', 'bb_width_pct']
        scaler = MinMaxScaler(feature_range=(0, 1))
        train_scaled = scaler.fit_transform(train_indicators[feature_cols].values)

        # Create sequences with technical indicators (add Date column back)
        train_scaled_df = pd.DataFrame(train_scaled, columns=feature_cols, index=train_indicators.index)
        train_scaled_df['Date'] = train_indicators['Date'].values
        X_train, y_train = create_sequences(train_scaled_df, feature_cols)

        print(f"입력 형태: {X_train.shape}", file=sys.stderr)  # Should show (samples, 75, 6)
        print(f"학습 라벨 비율:", round(y_train.mean(), 3), file=sys.stderr)

        # Calculate bias offset and temperature (mirroring fine-tuning implementation)
        bias = float(y_train.mean() - 0.5)

        # Temperature calibration (from fine-tuning implementation)
        epsilon = 1e-6
        p_pos = y_train.mean()
        logit_bias = np.log((p_pos + epsilon) / (1 - p_pos + epsilon))
        temperature = 1.8  # Fixed temperature value from fine-tuning

        print(f"편향 오프셋(bias):", round(bias, 3), file=sys.stderr)
        print(f"Logit 편향 (b): {logit_bias:.4f}, 온도 (T): {temperature}", file=sys.stderr)

        # Build simplified model
        model = build_lstm_model((WINDOW_DAYS, 6))

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=3,
            restore_best_weights=True,
            verbose=2
        )

        # Train model (no class weights, no manual validation split)
        model.fit(
            X_train, y_train,
            epochs=20,
            batch_size=32,
            validation_split=0.15,
            callbacks=[early_stopping],
            shuffle=False,  # Preserve time series order
            verbose=0
        )

        # Save model, scaler, bias, and temperature calibration parameters
        ROOT_DIR = Path(__file__).resolve().parents[1]
        model_dir = ROOT_DIR / "data" / "lstm_results"
        model_dir.mkdir(parents=True, exist_ok=True)

        model.save(model_dir / f"{ticker}_model_w75_h5_rsi_bb.keras")
        joblib.dump(scaler, model_dir / f"{ticker}_scaler_w75.pkl")

        # Save bias and temperature calibration parameters to JSON
        calibration_data = {
            "bias": bias,
            "logit_bias": logit_bias,
            "temperature": temperature,
            "epsilon": epsilon
        }
        with open(model_dir / f"{ticker}_calibration.json", 'w') as f:
            json.dump(calibration_data, f)

        # Compute technical indicators on all data for predictions
        all_indicators = compute_indicators(all_data)

        # Generate 6-day predictions (-4, -3, -2, -1, 0, +1) relative to reference date
        # But only for dates where we have sufficient historical data (75+ days)
        reference_ts = pd.Timestamp(REFERENCE_DATE)
        all_possible_dates = pd.bdate_range(start=reference_ts - BDay(4), end=reference_ts + BDay(1), freq='B')

        # Filter dates to only those with sufficient historical data in indicators
        dates_to_predict = []
        for d in all_possible_dates:
            date_mask = all_indicators['Date'] <= d
            available_rows = date_mask.sum()
            if available_rows >= WINDOW_DAYS:
                dates_to_predict.append(d)
            else:
                print(f"Skipping {d.date()}: only {available_rows} rows available, need {WINDOW_DAYS}", file=sys.stderr)

        if not dates_to_predict:
            print("No dates available for prediction with sufficient historical data", file=sys.stderr)
            sys.exit(1)

        print(f"Predicting for {len(dates_to_predict)} dates: {[d.date() for d in dates_to_predict]}", file=sys.stderr)

        predictions, y_true, y_pred = [], [], []

        for d in dates_to_predict:
            try:
                # Build sequence for prediction (use the date itself, not d-1)
                seq = build_sequence(all_data, all_indicators, scaler, d)

                # Get raw prediction and apply temperature calibration (from fine-tuning implementation)
                p_raw = float(model.predict(seq, verbose=0)[0][0])

                # Apply temperature calibration
                prob_clipped = np.clip(p_raw, epsilon, 1 - epsilon)
                logit = np.log(prob_clipped / (1 - prob_clipped))
                calibrated_prob = sigmoid((logit + logit_bias) / temperature)
                p_adj = np.clip(calibrated_prob, 0.0, 1.0)
                lbl = int(p_adj > 0.5)

                # Get actual label if available
                act = get_actual_label_if_available(all_data, d)
                if act is not None:
                    y_true.append(act)
                    y_pred.append(lbl)
                    # Removed traffic history CSV update - no longer needed

                predictions.append({
                    "date": d.strftime("%Y-%m-%d"),
                    "pred_prob_up": round(p_adj, 4),
                    "pred_prob_down": round(1 - p_adj, 4),
                    "predicted_label": lbl,
                    "actual_label": act,
                    "prediction_horizon": 5
                })

            except Exception as e:
                # Handle both Timestamp and other date types
                date_str = d.strftime('%Y-%m-%d') if hasattr(d, 'strftime') else str(d)
                print(f"Error predicting for {date_str}: {e}", file=sys.stderr)
                continue

        # Calculate metrics using first 5 days that have labels
        if y_true:
            metrics = {
                "accuracy": round(accuracy_score(y_true, y_pred), 3),
                "precision": round(precision_score(y_true, y_pred, zero_division=0), 3),
                "recall": round(recall_score(y_true, y_pred, zero_division=0), 3),
                "f1": round(f1_score(y_true, y_pred, zero_division=0), 3)
            }
        else:
            metrics = {k: None for k in ["accuracy", "precision", "recall", "f1"]}

        print(f"성능 지표:", metrics, file=sys.stderr)

        # Check if we have any predictions for traffic light calculation
        if not predictions:
            print("No predictions available for traffic light calculation", file=sys.stderr)
            tl_color = "red"  # Default to red if no predictions
        else:
            # ── Traffic light thresholds for SpeedLight compatibility:
            #   prob_up < 0.475           → RED
            #   0.475 ≤ prob_up < 0.525   → YELLOW
            #   prob_up ≥ 0.525           → GREEN
            prob_up_next = predictions[-1]["pred_prob_up"]  # day +1 (last prediction)
            if prob_up_next < 0.475:
                tl_color = "red"
            elif prob_up_next >= 0.525:
                tl_color = "green"
            else:
                tl_color = "yellow"

        print(f"신호등:", tl_color, file=sys.stderr)

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_until.date().strftime("%Y-%m-%d"),
            "predictions": predictions,
            "traffic_light": tl_color,
            "metrics": metrics
        }

        # Output result as JSON
        print(json.dumps(result))

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()