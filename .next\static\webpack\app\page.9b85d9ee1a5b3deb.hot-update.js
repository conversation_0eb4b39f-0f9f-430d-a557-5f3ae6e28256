"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SpeedTraffic.tsx":
/*!*****************************************!*\
  !*** ./src/components/SpeedTraffic.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_companyLookup__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/companyLookup */ \"(app-pages-browser)/./src/utils/companyLookup.ts\");\n\nvar _s = $RefreshSig$();\n\n\nconst SpeedTraffic = (param)=>{\n    let { symbol, onPhaseMessage, onAnalysisComplete } = param;\n    _s();\n    // Market indicators state (Pre-ticker mode)\n    const [indicators, setIndicators] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [lastUpdate, setLastUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Traffic lights state (Post-ticker mode) - New order\n    const [technicalLight, setTechnicalLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 1: Technical Analysis\n    const [industryLight, setIndustryLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 2: Industry Sensitivity\n    const [marketLight, setMarketLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 3: Market Sensitivity (CAPM)\n    const [riskLight, setRiskLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 4: Volatility Risk\n    const [neuralLight, setNeuralLight] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inactive'); // Light 5: Neural Network (LSTM)\n    // Prediction state\n    const [phase1Loading, setPhase1Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [phase2Loading, setPhase2Loading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lstmLoading, setLstmLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [predictionError, setPredictionError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showTimeoutMessage, setShowTimeoutMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastRequestTime, setLastRequestTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Results storage state\n    const [allResults, setAllResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Single-flight guard\n    const inFlight = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // 실제 시장 데이터 가져오기 (Pre-ticker mode)\n    const fetchMarketData = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch('/api/market_data');\n            const result = await response.json();\n            if (result.success && result.data) {\n                const formattedData = result.data.map((item)=>({\n                        label: item.label,\n                        value: item.price.toLocaleString('en-US', {\n                            minimumFractionDigits: 2,\n                            maximumFractionDigits: 2\n                        }),\n                        change: \"\".concat(item.changePercent >= 0 ? '+' : '').concat(item.changePercent.toFixed(2), \"%\"),\n                        trend: item.trend,\n                        color: item.trend === 'up' ? 'green' : item.trend === 'down' ? 'red' : 'yellow'\n                    }));\n                setIndicators(formattedData);\n                setLastUpdate(new Date().toLocaleTimeString('ko-KR'));\n            }\n        } catch (error) {\n            console.error('Failed to fetch market data:', error);\n            // 에러 시 fallback 데이터 사용\n            setIndicators([\n                {\n                    label: 'S&P 500',\n                    value: '4,567.89',\n                    change: '+1.2%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '나스닥',\n                    value: '14,234.56',\n                    change: '+0.8%',\n                    trend: 'up',\n                    color: 'green'\n                },\n                {\n                    label: '다우존스',\n                    value: '34,567.12',\n                    change: '-0.3%',\n                    trend: 'down',\n                    color: 'red'\n                },\n                {\n                    label: 'VIX',\n                    value: '18.45',\n                    change: '-2.1%',\n                    trend: 'down',\n                    color: 'yellow'\n                },\n                {\n                    label: '달러/원',\n                    value: '1,327.50',\n                    change: '+0.5%',\n                    trend: 'up',\n                    color: 'green'\n                }\n            ]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Convert result color to traffic light status\n    const resultColorToStatus = (color)=>{\n        switch(color === null || color === void 0 ? void 0 : color.toLowerCase()){\n            case 'green':\n            case 'good':\n                return 'good';\n            case 'yellow':\n            case 'warning':\n                return 'warning';\n            case 'red':\n            case 'danger':\n                return 'danger';\n            case 'inactive':\n            case 'grey':\n            case 'gray':\n                return 'inactive';\n            default:\n                return 'warning';\n        }\n    };\n    // Extract color from LSTM/MFI results\n    const getColorFromResult = (obj)=>{\n        if (!obj) return undefined;\n        if (typeof obj === 'string') return obj;\n        if (obj.result_color) return obj.result_color;\n        if (obj.traffic_light) return obj.traffic_light;\n        if (obj.color) return obj.color;\n        return undefined;\n    };\n    // Staged execution: Phase 1 (Fast services) + Phase 2 (LSTM)\n    const fetchStagedPrediction = async ()=>{\n        if (!symbol || inFlight.current) return;\n        // Prevent too frequent requests (minimum 10 seconds between requests)\n        const now = Date.now();\n        if (now - lastRequestTime < 10000) {\n            console.log('Prediction request throttled - too frequent');\n            return;\n        }\n        try {\n            var _phase2Result_traffic_lights;\n            // Set single-flight guard\n            inFlight.current = true;\n            setLastRequestTime(now);\n            // Reset all lights to inactive state\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n            setPredictionError(null);\n            setShowTimeoutMessage(false);\n            // Get company name for user feedback\n            const companyName = (0,_utils_companyLookup__WEBPACK_IMPORTED_MODULE_2__.getCompanyName)(symbol);\n            // Initial user feedback message\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage(\"\\uD83D\\uDE80 \".concat(companyName, \" 차트 분석을 시작할게요! \\uD83D\\uDCCA\"));\n            // Wait 1.5 seconds before starting Phase 1\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            console.log(\"[SpeedTraffic] Starting staged prediction for \".concat(symbol));\n            // Phase 1: Execute fast services (Technical, Industry, Market, Volatility)\n            setPhase1Loading(true);\n            console.log(\"[SpeedTraffic] Phase 1: Starting fast services for \".concat(symbol));\n            const phase1Response = await fetch(\"/api/speedtraffic_staged?symbol=\".concat(symbol, \"&stage=phase1\"), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(30000)\n            });\n            if (!phase1Response.ok) {\n                throw new Error(\"Phase 1 HTTP \".concat(phase1Response.status, \": \").concat(phase1Response.statusText));\n            }\n            const phase1Result = await phase1Response.json();\n            console.log(\"[SpeedTraffic] Phase 1 result:\", phase1Result);\n            // Update lights 1-4 immediately after Phase 1 completes\n            if (phase1Result.traffic_lights) {\n                if (phase1Result.traffic_lights.technical) {\n                    setTechnicalLight(resultColorToStatus(phase1Result.traffic_lights.technical));\n                    console.log(\"[SpeedTraffic] Technical light set to: \".concat(phase1Result.traffic_lights.technical));\n                }\n                if (phase1Result.traffic_lights.industry) {\n                    setIndustryLight(resultColorToStatus(phase1Result.traffic_lights.industry));\n                    console.log(\"[SpeedTraffic] Industry light set to: \".concat(phase1Result.traffic_lights.industry));\n                }\n                if (phase1Result.traffic_lights.market) {\n                    setMarketLight(resultColorToStatus(phase1Result.traffic_lights.market));\n                    console.log(\"[SpeedTraffic] Market light set to: \".concat(phase1Result.traffic_lights.market));\n                }\n                if (phase1Result.traffic_lights.risk) {\n                    setRiskLight(resultColorToStatus(phase1Result.traffic_lights.risk));\n                    console.log(\"[SpeedTraffic] Risk light set to: \".concat(phase1Result.traffic_lights.risk));\n                }\n            }\n            // Store Phase 1 results\n            setAllResults((prev)=>({\n                    ...prev,\n                    symbol,\n                    mfi: phase1Result.mfi,\n                    bollinger: phase1Result.bollinger,\n                    rsi: phase1Result.rsi,\n                    industry: phase1Result.industry,\n                    capm: phase1Result.capm,\n                    garch: phase1Result.garch,\n                    traffic_lights: {\n                        ...prev.traffic_lights,\n                        ...phase1Result.traffic_lights\n                    }\n                }));\n            setPhase1Loading(false);\n            console.log(\"[SpeedTraffic] Phase 1 completed successfully for \".concat(symbol));\n            // Send Phase 1 completion message to chat\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('기술적 분석, 산업 민감도, 시장 민감도, 변동성 리스크 분석을 마쳤어요! 📊');\n            // Wait 1.5 seconds before starting Phase 2\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            // Send Phase 2 entry message to chat\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('이제 딥러닝 기반 가격 변동 예측을 진행합니다. 잠시만 기다려 주세요! 🤖');\n            // Phase 2: Execute LSTM service\n            setPhase2Loading(true);\n            setLstmLoading(true);\n            console.log(\"[SpeedTraffic] Phase 2: Starting LSTM service for \".concat(symbol));\n            // Start 20-second timer for Korean timeout message\n            const timeoutTimer = setTimeout(()=>{\n                setShowTimeoutMessage(true);\n            }, 20000);\n            const phase2Response = await fetch(\"/api/speedtraffic_staged?symbol=\".concat(symbol, \"&stage=phase2\"), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                },\n                signal: AbortSignal.timeout(60000)\n            });\n            clearTimeout(timeoutTimer);\n            if (!phase2Response.ok) {\n                throw new Error(\"Phase 2 HTTP \".concat(phase2Response.status, \": \").concat(phase2Response.statusText));\n            }\n            const phase2Result = await phase2Response.json();\n            console.log(\"[SpeedTraffic] Phase 2 result:\", phase2Result);\n            // Update light 5 after Phase 2 completes\n            if (phase2Result.traffic_lights && phase2Result.traffic_lights.neural) {\n                setNeuralLight(resultColorToStatus(phase2Result.traffic_lights.neural));\n                console.log(\"[SpeedTraffic] Neural light set to: \".concat(phase2Result.traffic_lights.neural));\n            }\n            // Store Phase 2 results and save complete analysis\n            const finalResults = {\n                symbol,\n                companyName: (0,_utils_companyLookup__WEBPACK_IMPORTED_MODULE_2__.getCompanyName)(symbol),\n                timestamp: new Date().toISOString(),\n                analysisDate: new Date().toISOString().split('T')[0],\n                lstm: phase2Result.lstm,\n                mfi: allResults.mfi,\n                bollinger: allResults.bollinger,\n                rsi: allResults.rsi,\n                industry: allResults.industry,\n                capm: allResults.capm,\n                garch: allResults.garch,\n                traffic_lights: {\n                    ...allResults.traffic_lights,\n                    neural: (_phase2Result_traffic_lights = phase2Result.traffic_lights) === null || _phase2Result_traffic_lights === void 0 ? void 0 : _phase2Result_traffic_lights.neural\n                }\n            };\n            // Save comprehensive results to file via API\n            try {\n                const saveResponse = await fetch('/api/save_analysis_results', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(finalResults)\n                });\n                if (saveResponse.ok) {\n                    const saveResult = await saveResponse.json();\n                    console.log(\"[SpeedTraffic] Analysis results saved for \".concat(symbol, \":\"), saveResult.filepath);\n                } else {\n                    console.error(\"[SpeedTraffic] Failed to save results for \".concat(symbol, \":\"), await saveResponse.text());\n                }\n            } catch (error) {\n                console.error(\"[SpeedTraffic] Error saving results for \".concat(symbol, \":\"), error);\n            }\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            console.log(\"[SpeedTraffic] Phase 2 completed successfully for \".concat(symbol));\n            // Notify parent component of analysis completion\n            onAnalysisComplete === null || onAnalysisComplete === void 0 ? void 0 : onAnalysisComplete(finalResults);\n            // Send Phase 2 completion message to chat\n            onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('모든 분석이 완료되었습니다! 결과를 확인해 보세요. ✨');\n            // Wait 1.5 seconds, then display report prompt\n            setTimeout(()=>{\n                onPhaseMessage === null || onPhaseMessage === void 0 ? void 0 : onPhaseMessage('모든 데이터가 준비되었네요. SpeedTraffic 분석 보고서가 필요하신가요? 📊', true);\n            }, 1500);\n            console.log(\"[SpeedTraffic] All phases completed successfully for \".concat(symbol));\n        } catch (error) {\n            console.error('Staged prediction error:', error);\n            if (error instanceof Error) {\n                if (error.name === 'TimeoutError') {\n                    setPredictionError('요청 시간 초과');\n                } else {\n                    setPredictionError(\"예측 실패: \".concat(error.message));\n                }\n            } else {\n                setPredictionError('예측 서비스 연결 실패');\n            }\n            // Reset all lights to inactive on error\n            setTechnicalLight('inactive');\n            setIndustryLight('inactive');\n            setMarketLight('inactive');\n            setRiskLight('inactive');\n            setNeuralLight('inactive');\n        } finally{\n            setPhase1Loading(false);\n            setPhase2Loading(false);\n            setLstmLoading(false);\n            setShowTimeoutMessage(false);\n            inFlight.current = false;\n        }\n    };\n    // Effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (symbol) {\n                // When symbol is provided, fetch staged prediction once\n                fetchStagedPrediction();\n            } else {\n                // When no symbol, fetch market data initially\n                fetchMarketData();\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // 20초마다 시장 데이터 업데이트 (Pre-ticker mode only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SpeedTraffic.useEffect\": ()=>{\n            if (!symbol) {\n                const interval = setInterval({\n                    \"SpeedTraffic.useEffect.interval\": ()=>{\n                        fetchMarketData();\n                    }\n                }[\"SpeedTraffic.useEffect.interval\"], 20000);\n                return ({\n                    \"SpeedTraffic.useEffect\": ()=>clearInterval(interval)\n                })[\"SpeedTraffic.useEffect\"];\n            }\n        }\n    }[\"SpeedTraffic.useEffect\"], [\n        symbol\n    ]);\n    // Utility functions for rendering\n    const getTrendIcon = (trend)=>{\n        switch(trend){\n            case 'up':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M7 17l9.2-9.2M17 17V7H7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 11\n                }, undefined);\n            case 'down':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-red-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M17 7l-9.2 9.2M7 7v10h10\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-slate-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M20 12H4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const getChangeColor = (trend)=>{\n        switch(trend){\n            case 'up':\n                return 'text-green-600';\n            case 'down':\n                return 'text-red-600';\n            default:\n                return 'text-slate-500';\n        }\n    };\n    const getTrafficLightColor = (status)=>{\n        switch(status){\n            case 'good':\n                return 'bg-green-500';\n            case 'warning':\n                return 'bg-yellow-500';\n            case 'danger':\n                return 'bg-red-500';\n            default:\n                return 'bg-gray-400';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'good':\n                return '양호';\n            case 'warning':\n                return '보통';\n            case 'danger':\n                return '주의';\n            case 'inactive':\n                return '비활성화';\n            default:\n                return '분석중';\n        }\n    };\n    // Post-ticker mode: Traffic lights display\n    if (symbol) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4 max-w-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-transparent bg-gradient-to-r from-blue-400 via-purple-500 to-blue-600 bg-clip-text\",\n                        children: \"SpeedTraffic™\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-7 shadow-xl border border-slate-700 min-h-[320px]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(technicalLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(technicalLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(technicalLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        technicalLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(technicalLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(technicalLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"기술적 분석\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(technicalLight === 'inactive' ? 'text-gray-400' : technicalLight === 'good' ? 'text-green-300' : technicalLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: technicalLight === 'inactive' ? '대기중' : getStatusText(technicalLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(industryLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(industryLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(industryLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        industryLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(industryLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(industryLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"산업 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(industryLight === 'inactive' ? 'text-gray-400' : industryLight === 'good' ? 'text-green-300' : industryLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: industryLight === 'inactive' ? '대기중' : getStatusText(industryLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(marketLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(marketLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(marketLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        marketLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(marketLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(marketLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"시장 민감도\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(marketLight === 'inactive' ? 'text-gray-400' : marketLight === 'good' ? 'text-green-300' : marketLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: marketLight === 'inactive' ? '대기중' : getStatusText(marketLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(riskLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(riskLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(riskLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        riskLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(riskLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(riskLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: \"변동성 리스크\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 591,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(riskLight === 'inactive' ? 'text-gray-400' : riskLight === 'good' ? 'text-green-300' : riskLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: riskLight === 'inactive' ? '대기중' : getStatusText(riskLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full \".concat(neuralLight === 'inactive' ? 'bg-gray-500' : getTrafficLightColor(neuralLight), \" shadow-lg transition-all duration-700 ease-in-out \").concat(neuralLight === 'inactive' ? '' : 'animate-pulse')\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 613,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        neuralLight !== 'inactive' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full \".concat(getTrafficLightColor(neuralLight), \" opacity-20 animate-ping\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        lstmLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 w-6 h-6 rounded-full border-2 border-blue-500 border-t-transparent animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium \".concat(neuralLight === 'inactive' ? 'text-gray-400' : 'text-white group-hover:text-blue-300', \" transition-colors\"),\n                                                    children: lstmLoading ? '딥러닝 기반 예측(LSTM)' : '신경망 예측(LSTM)'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 611,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs px-2 py-1 font-medium whitespace-nowrap \".concat(neuralLight === 'inactive' ? 'text-gray-400' : neuralLight === 'good' ? 'text-green-300' : neuralLight === 'warning' ? 'text-yellow-300' : 'text-red-300'),\n                                            children: lstmLoading ? '분석중...' : neuralLight === 'inactive' ? '대기중' : getStatusText(neuralLight)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                            lineNumber: 632,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 609,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 483,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, undefined),\n                phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-slate-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-slate-600\",\n                                children: \"딥러닝 기반 예측(LSTM) 분석 중...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 651,\n                    columnNumber: 11\n                }, undefined),\n                showTimeoutMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-600 text-center\",\n                        children: \"이 작업은 시간이 걸립니다... (최대 60초)\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 664,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-red-500/10 border border-red-500/20 rounded-lg p-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-xs text-red-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-3 h-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                    lineNumber: 675,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"오류: \",\n                                    predictionError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 677,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 673,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 672,\n                    columnNumber: 11\n                }, undefined),\n                predictionError && !phase1Loading && !phase2Loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: fetchStagedPrediction,\n                        className: \"px-4 py-2 bg-blue-500 text-white rounded-lg text-sm hover:bg-blue-600 transition-colors\",\n                        children: \"다시 시도\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 685,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n            lineNumber: 472,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Pre-ticker mode: Market indicators display\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-slate-900\",\n                        children: \"시장 현황\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 rounded-full animate-pulse \".concat(loading ? 'bg-yellow-400' : 'bg-green-400')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 703,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: loading && indicators.length === 0 ? // 로딩 스켈레톤\n                Array.from({\n                    length: 5\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-16\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-4 h-4 bg-slate-200 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-slate-200 rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-slate-200 rounded w-12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 720,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 13\n                    }, undefined)) : indicators.map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-slate-50 rounded-lg border border-slate-100 hover:bg-slate-100 transition-colors duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-slate-700\",\n                                        children: indicator.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    getTrendIcon(indicator.trend)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg font-semibold text-slate-900\",\n                                        children: indicator.value\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 735,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium \".concat(getChangeColor(indicator.trend)),\n                                        children: indicator.change\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 709,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-3 border-t border-slate-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-2 text-xs text-slate-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-3 h-3\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 748,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: loading ? '업데이트 중...' : lastUpdate ? \"마지막 업데이트: \".concat(lastUpdate) : '20초마다 업데이트'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                    lineNumber: 747,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n                lineNumber: 746,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\home\\\\ubuntu\\\\financial_dashboard\\\\src\\\\components\\\\SpeedTraffic.tsx\",\n        lineNumber: 701,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SpeedTraffic, \"rkIZtL8CAwQQhNNcBHW9I/tTYYk=\");\n_c = SpeedTraffic;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SpeedTraffic);\nvar _c;\n$RefreshReg$(_c, \"SpeedTraffic\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1NwZWVkVHJhZmZpYy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEyRDtBQUNIO0FBMkV4RCxNQUFNSyxlQUE0QztRQUFDLEVBQUVDLE1BQU0sRUFBRUMsY0FBYyxFQUFFQyxrQkFBa0IsRUFBRTs7SUFDL0YsNENBQTRDO0lBQzVDLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHVCwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUNsRSxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDWSxZQUFZQyxjQUFjLEdBQUdiLCtDQUFRQSxDQUFTO0lBRXJELHNEQUFzRDtJQUN0RCxNQUFNLENBQUNjLGdCQUFnQkMsa0JBQWtCLEdBQUdmLCtDQUFRQSxDQUE2QyxhQUFhLDhCQUE4QjtJQUM1SSxNQUFNLENBQUNnQixlQUFlQyxpQkFBaUIsR0FBR2pCLCtDQUFRQSxDQUE2QyxhQUFhLGdDQUFnQztJQUM1SSxNQUFNLENBQUNrQixhQUFhQyxlQUFlLEdBQUduQiwrQ0FBUUEsQ0FBNkMsYUFBYSxxQ0FBcUM7SUFDN0ksTUFBTSxDQUFDb0IsV0FBV0MsYUFBYSxHQUFHckIsK0NBQVFBLENBQTZDLGFBQWEsMkJBQTJCO0lBQy9ILE1BQU0sQ0FBQ3NCLGFBQWFDLGVBQWUsR0FBR3ZCLCtDQUFRQSxDQUE2QyxhQUFhLGlDQUFpQztJQUV6SSxtQkFBbUI7SUFDbkIsTUFBTSxDQUFDd0IsZUFBZUMsaUJBQWlCLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUMwQixlQUFlQyxpQkFBaUIsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzRCLGFBQWFDLGVBQWUsR0FBRzdCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzhCLGlCQUFpQkMsbUJBQW1CLEdBQUcvQiwrQ0FBUUEsQ0FBZ0I7SUFDdEUsTUFBTSxDQUFDZ0Msb0JBQW9CQyxzQkFBc0IsR0FBR2pDLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQ2tDLGlCQUFpQkMsbUJBQW1CLEdBQUduQywrQ0FBUUEsQ0FBUztJQUUvRCx3QkFBd0I7SUFDeEIsTUFBTSxDQUFDb0MsWUFBWUMsY0FBYyxHQUFHckMsK0NBQVFBLENBQTJCLENBQUM7SUFJeEUsc0JBQXNCO0lBQ3RCLE1BQU1zQyxXQUFXcEMsNkNBQU1BLENBQUM7SUFFeEIsbUNBQW1DO0lBQ25DLE1BQU1xQyxrQkFBa0I7UUFDdEIsSUFBSTtZQUNGNUIsV0FBVztZQUNYLE1BQU02QixXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sSUFBSUYsT0FBT0csSUFBSSxFQUFFO2dCQUNqQyxNQUFNQyxnQkFBZ0JKLE9BQU9HLElBQUksQ0FBQ0UsR0FBRyxDQUFDLENBQUNDLE9BQWU7d0JBQ3BEQyxPQUFPRCxLQUFLQyxLQUFLO3dCQUNqQkMsT0FBT0YsS0FBS0csS0FBSyxDQUFDQyxjQUFjLENBQUMsU0FBUzs0QkFDeENDLHVCQUF1Qjs0QkFDdkJDLHVCQUF1Qjt3QkFDekI7d0JBQ0FDLFFBQVEsR0FBd0NQLE9BQXJDQSxLQUFLUSxhQUFhLElBQUksSUFBSSxNQUFNLElBQW1DLE9BQTlCUixLQUFLUSxhQUFhLENBQUNDLE9BQU8sQ0FBQyxJQUFHO3dCQUM5RUMsT0FBT1YsS0FBS1UsS0FBSzt3QkFDakJDLE9BQU9YLEtBQUtVLEtBQUssS0FBSyxPQUFPLFVBQVVWLEtBQUtVLEtBQUssS0FBSyxTQUFTLFFBQVE7b0JBQ3pFO2dCQUVBakQsY0FBY3FDO2dCQUNkakMsY0FBYyxJQUFJK0MsT0FBT0Msa0JBQWtCLENBQUM7WUFDOUM7UUFDRixFQUFFLE9BQU9DLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsdUJBQXVCO1lBQ3ZCckQsY0FBYztnQkFDWjtvQkFBRXdDLE9BQU87b0JBQVdDLE9BQU87b0JBQVlLLFFBQVE7b0JBQVNHLE9BQU87b0JBQU1DLE9BQU87Z0JBQVE7Z0JBQ3BGO29CQUFFVixPQUFPO29CQUFPQyxPQUFPO29CQUFhSyxRQUFRO29CQUFTRyxPQUFPO29CQUFNQyxPQUFPO2dCQUFRO2dCQUNqRjtvQkFBRVYsT0FBTztvQkFBUUMsT0FBTztvQkFBYUssUUFBUTtvQkFBU0csT0FBTztvQkFBUUMsT0FBTztnQkFBTTtnQkFDbEY7b0JBQUVWLE9BQU87b0JBQU9DLE9BQU87b0JBQVNLLFFBQVE7b0JBQVNHLE9BQU87b0JBQVFDLE9BQU87Z0JBQVM7Z0JBQ2hGO29CQUFFVixPQUFPO29CQUFRQyxPQUFPO29CQUFZSyxRQUFRO29CQUFTRyxPQUFPO29CQUFNQyxPQUFPO2dCQUFRO2FBQ2xGO1FBQ0gsU0FBVTtZQUNSaEQsV0FBVztRQUNiO0lBQ0Y7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTXFELHNCQUFzQixDQUFDTDtRQUMzQixPQUFRQSxrQkFBQUEsNEJBQUFBLE1BQU9NLFdBQVc7WUFDeEIsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsT0FBTztZQUNULEtBQUs7WUFDTCxLQUFLO2dCQUNILE9BQU87WUFDVCxLQUFLO1lBQ0wsS0FBSztnQkFDSCxPQUFPO1lBQ1QsS0FBSztZQUNMLEtBQUs7WUFDTCxLQUFLO2dCQUNILE9BQU87WUFDVDtnQkFDRSxPQUFPO1FBQ1g7SUFDRjtJQUVBLHNDQUFzQztJQUN0QyxNQUFNQyxxQkFBcUIsQ0FBQ0M7UUFDMUIsSUFBSSxDQUFDQSxLQUFLLE9BQU9DO1FBQ2pCLElBQUksT0FBT0QsUUFBUSxVQUFVLE9BQU9BO1FBQ3BDLElBQUlBLElBQUlFLFlBQVksRUFBRSxPQUFPRixJQUFJRSxZQUFZO1FBQzdDLElBQUlGLElBQUlHLGFBQWEsRUFBRSxPQUFPSCxJQUFJRyxhQUFhO1FBQy9DLElBQUlILElBQUlSLEtBQUssRUFBRSxPQUFPUSxJQUFJUixLQUFLO1FBQy9CLE9BQU9TO0lBQ1Q7SUFFQSw2REFBNkQ7SUFDN0QsTUFBTUcsd0JBQXdCO1FBQzVCLElBQUksQ0FBQ2xFLFVBQVVpQyxTQUFTa0MsT0FBTyxFQUFFO1FBRWpDLHNFQUFzRTtRQUN0RSxNQUFNQyxNQUFNYixLQUFLYSxHQUFHO1FBQ3BCLElBQUlBLE1BQU12QyxrQkFBa0IsT0FBTztZQUNqQzZCLFFBQVFXLEdBQUcsQ0FBQztZQUNaO1FBQ0Y7UUFFQSxJQUFJO2dCQTZJVUM7WUE1SVosMEJBQTBCO1lBQzFCckMsU0FBU2tDLE9BQU8sR0FBRztZQUNuQnJDLG1CQUFtQnNDO1lBRW5CLHFDQUFxQztZQUNyQzFELGtCQUFrQjtZQUNsQkUsaUJBQWlCO1lBQ2pCRSxlQUFlO1lBQ2ZFLGFBQWE7WUFDYkUsZUFBZTtZQUVmUSxtQkFBbUI7WUFDbkJFLHNCQUFzQjtZQUV0QixxQ0FBcUM7WUFDckMsTUFBTTJDLGNBQWN6RSxvRUFBY0EsQ0FBQ0U7WUFFbkMsZ0NBQWdDO1lBQ2hDQywyQkFBQUEscUNBQUFBLGVBQWlCLGdCQUFrQixPQUFac0UsYUFBWTtZQUVuQywyQ0FBMkM7WUFDM0MsTUFBTSxJQUFJQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO1lBRWpEZixRQUFRVyxHQUFHLENBQUMsaURBQXdELE9BQVByRTtZQUU3RCwyRUFBMkU7WUFDM0VvQixpQkFBaUI7WUFDakJzQyxRQUFRVyxHQUFHLENBQUMsc0RBQTZELE9BQVByRTtZQUVsRSxNQUFNMkUsaUJBQWlCLE1BQU12QyxNQUFNLG1DQUEwQyxPQUFQcEMsUUFBTyxrQkFBZ0I7Z0JBQzNGNEUsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxVQUFVO2dCQUNaO2dCQUNBQyxRQUFRQyxZQUFZQyxPQUFPLENBQUM7WUFDOUI7WUFFQSxJQUFJLENBQUNMLGVBQWVNLEVBQUUsRUFBRTtnQkFDdEIsTUFBTSxJQUFJQyxNQUFNLGdCQUEwQ1AsT0FBMUJBLGVBQWVRLE1BQU0sRUFBQyxNQUE4QixPQUExQlIsZUFBZVMsVUFBVTtZQUNyRjtZQUVBLE1BQU1DLGVBQWUsTUFBTVYsZUFBZXJDLElBQUk7WUFDOUNvQixRQUFRVyxHQUFHLENBQUUsa0NBQWlDZ0I7WUFFOUMsd0RBQXdEO1lBQ3hELElBQUlBLGFBQWFDLGNBQWMsRUFBRTtnQkFDL0IsSUFBSUQsYUFBYUMsY0FBYyxDQUFDQyxTQUFTLEVBQUU7b0JBQ3pDN0Usa0JBQWtCaUQsb0JBQW9CMEIsYUFBYUMsY0FBYyxDQUFDQyxTQUFTO29CQUMzRTdCLFFBQVFXLEdBQUcsQ0FBQywwQ0FBZ0YsT0FBdENnQixhQUFhQyxjQUFjLENBQUNDLFNBQVM7Z0JBQzdGO2dCQUNBLElBQUlGLGFBQWFDLGNBQWMsQ0FBQ0UsUUFBUSxFQUFFO29CQUN4QzVFLGlCQUFpQitDLG9CQUFvQjBCLGFBQWFDLGNBQWMsQ0FBQ0UsUUFBUTtvQkFDekU5QixRQUFRVyxHQUFHLENBQUMseUNBQThFLE9BQXJDZ0IsYUFBYUMsY0FBYyxDQUFDRSxRQUFRO2dCQUMzRjtnQkFDQSxJQUFJSCxhQUFhQyxjQUFjLENBQUNHLE1BQU0sRUFBRTtvQkFDdEMzRSxlQUFlNkMsb0JBQW9CMEIsYUFBYUMsY0FBYyxDQUFDRyxNQUFNO29CQUNyRS9CLFFBQVFXLEdBQUcsQ0FBQyx1Q0FBMEUsT0FBbkNnQixhQUFhQyxjQUFjLENBQUNHLE1BQU07Z0JBQ3ZGO2dCQUNBLElBQUlKLGFBQWFDLGNBQWMsQ0FBQ0ksSUFBSSxFQUFFO29CQUNwQzFFLGFBQWEyQyxvQkFBb0IwQixhQUFhQyxjQUFjLENBQUNJLElBQUk7b0JBQ2pFaEMsUUFBUVcsR0FBRyxDQUFDLHFDQUFzRSxPQUFqQ2dCLGFBQWFDLGNBQWMsQ0FBQ0ksSUFBSTtnQkFDbkY7WUFDRjtZQUVBLHdCQUF3QjtZQUN4QjFELGNBQWMyRCxDQUFBQSxPQUFTO29CQUNyQixHQUFHQSxJQUFJO29CQUNQM0Y7b0JBQ0E0RixLQUFLUCxhQUFhTyxHQUFHO29CQUNyQkMsV0FBV1IsYUFBYVEsU0FBUztvQkFDakNDLEtBQUtULGFBQWFTLEdBQUc7b0JBQ3JCTixVQUFVSCxhQUFhRyxRQUFRO29CQUMvQk8sTUFBTVYsYUFBYVUsSUFBSTtvQkFDdkJDLE9BQU9YLGFBQWFXLEtBQUs7b0JBQ3pCVixnQkFBZ0I7d0JBQ2QsR0FBR0ssS0FBS0wsY0FBYzt3QkFDdEIsR0FBR0QsYUFBYUMsY0FBYztvQkFDaEM7Z0JBQ0Y7WUFFQWxFLGlCQUFpQjtZQUNqQnNDLFFBQVFXLEdBQUcsQ0FBQyxxREFBNEQsT0FBUHJFO1lBRWpFLDBDQUEwQztZQUMxQ0MsMkJBQUFBLHFDQUFBQSxlQUFpQjtZQUVqQiwyQ0FBMkM7WUFDM0MsTUFBTSxJQUFJdUUsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztZQUVqRCxxQ0FBcUM7WUFDckN4RSwyQkFBQUEscUNBQUFBLGVBQWlCO1lBRWpCLGdDQUFnQztZQUNoQ3FCLGlCQUFpQjtZQUNqQkUsZUFBZTtZQUNma0MsUUFBUVcsR0FBRyxDQUFDLHFEQUE0RCxPQUFQckU7WUFFakUsbURBQW1EO1lBQ25ELE1BQU1pRyxlQUFldkIsV0FBVztnQkFDOUI5QyxzQkFBc0I7WUFDeEIsR0FBRztZQUVILE1BQU1zRSxpQkFBaUIsTUFBTTlELE1BQU0sbUNBQTBDLE9BQVBwQyxRQUFPLGtCQUFnQjtnQkFDM0Y0RSxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLFVBQVU7Z0JBQ1o7Z0JBQ0FDLFFBQVFDLFlBQVlDLE9BQU8sQ0FBQztZQUM5QjtZQUVBbUIsYUFBYUY7WUFFYixJQUFJLENBQUNDLGVBQWVqQixFQUFFLEVBQUU7Z0JBQ3RCLE1BQU0sSUFBSUMsTUFBTSxnQkFBMENnQixPQUExQkEsZUFBZWYsTUFBTSxFQUFDLE1BQThCLE9BQTFCZSxlQUFlZCxVQUFVO1lBQ3JGO1lBRUEsTUFBTWQsZUFBZSxNQUFNNEIsZUFBZTVELElBQUk7WUFDOUNvQixRQUFRVyxHQUFHLENBQUUsa0NBQWlDQztZQUU5Qyx5Q0FBeUM7WUFDekMsSUFBSUEsYUFBYWdCLGNBQWMsSUFBSWhCLGFBQWFnQixjQUFjLENBQUNjLE1BQU0sRUFBRTtnQkFDckVsRixlQUFleUMsb0JBQW9CVyxhQUFhZ0IsY0FBYyxDQUFDYyxNQUFNO2dCQUNyRTFDLFFBQVFXLEdBQUcsQ0FBQyx1Q0FBMEUsT0FBbkNDLGFBQWFnQixjQUFjLENBQUNjLE1BQU07WUFDdkY7WUFFQSxtREFBbUQ7WUFDbkQsTUFBTUMsZUFBZ0M7Z0JBQ3BDckc7Z0JBQ0F1RSxhQUFhekUsb0VBQWNBLENBQUNFO2dCQUM1QnNHLFdBQVcsSUFBSS9DLE9BQU9nRCxXQUFXO2dCQUNqQ0MsY0FBYyxJQUFJakQsT0FBT2dELFdBQVcsR0FBR0UsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNwREMsTUFBTXBDLGFBQWFvQyxJQUFJO2dCQUN2QmQsS0FBSzdELFdBQVc2RCxHQUFHO2dCQUNuQkMsV0FBVzlELFdBQVc4RCxTQUFTO2dCQUMvQkMsS0FBSy9ELFdBQVcrRCxHQUFHO2dCQUNuQk4sVUFBVXpELFdBQVd5RCxRQUFRO2dCQUM3Qk8sTUFBTWhFLFdBQVdnRSxJQUFJO2dCQUNyQkMsT0FBT2pFLFdBQVdpRSxLQUFLO2dCQUN2QlYsZ0JBQWdCO29CQUNkLEdBQUd2RCxXQUFXdUQsY0FBYztvQkFDNUJjLE1BQU0sR0FBRTlCLCtCQUFBQSxhQUFhZ0IsY0FBYyxjQUEzQmhCLG1EQUFBQSw2QkFBNkI4QixNQUFNO2dCQUM3QztZQUNGO1lBRUEsNkNBQTZDO1lBQzdDLElBQUk7Z0JBQ0YsTUFBTU8sZUFBZSxNQUFNdkUsTUFBTSw4QkFBOEI7b0JBQzdEd0MsUUFBUTtvQkFDUkMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO29CQUNBK0IsTUFBTUMsS0FBS0MsU0FBUyxDQUFDVDtnQkFDdkI7Z0JBRUEsSUFBSU0sYUFBYTFCLEVBQUUsRUFBRTtvQkFDbkIsTUFBTThCLGFBQWEsTUFBTUosYUFBYXJFLElBQUk7b0JBQzFDb0IsUUFBUVcsR0FBRyxDQUFDLDZDQUFvRCxPQUFQckUsUUFBTyxNQUFJK0csV0FBV0MsUUFBUTtnQkFDekYsT0FBTztvQkFDTHRELFFBQVFELEtBQUssQ0FBQyw2Q0FBb0QsT0FBUHpELFFBQU8sTUFBSSxNQUFNMkcsYUFBYU0sSUFBSTtnQkFDL0Y7WUFDRixFQUFFLE9BQU94RCxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkNBQWtELE9BQVB6RCxRQUFPLE1BQUl5RDtZQUN0RTtZQUVBbkMsaUJBQWlCO1lBQ2pCRSxlQUFlO1lBQ2ZrQyxRQUFRVyxHQUFHLENBQUMscURBQTRELE9BQVByRTtZQUVqRSxpREFBaUQ7WUFDakRFLCtCQUFBQSx5Q0FBQUEsbUJBQXFCbUc7WUFFckIsMENBQTBDO1lBQzFDcEcsMkJBQUFBLHFDQUFBQSxlQUFpQjtZQUVqQiwrQ0FBK0M7WUFDL0N5RSxXQUFXO2dCQUNUekUsMkJBQUFBLHFDQUFBQSxlQUFpQixtREFBbUQ7WUFDdEUsR0FBRztZQUVIeUQsUUFBUVcsR0FBRyxDQUFDLHdEQUErRCxPQUFQckU7UUFFdEUsRUFBRSxPQUFPeUQsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUUxQyxJQUFJQSxpQkFBaUJ5QixPQUFPO2dCQUMxQixJQUFJekIsTUFBTXlELElBQUksS0FBSyxnQkFBZ0I7b0JBQ2pDeEYsbUJBQW1CO2dCQUNyQixPQUFPO29CQUNMQSxtQkFBbUIsVUFBd0IsT0FBZCtCLE1BQU0wRCxPQUFPO2dCQUM1QztZQUNGLE9BQU87Z0JBQ0x6RixtQkFBbUI7WUFDckI7WUFFQSx3Q0FBd0M7WUFDeENoQixrQkFBa0I7WUFDbEJFLGlCQUFpQjtZQUNqQkUsZUFBZTtZQUNmRSxhQUFhO1lBQ2JFLGVBQWU7UUFFakIsU0FBVTtZQUNSRSxpQkFBaUI7WUFDakJFLGlCQUFpQjtZQUNqQkUsZUFBZTtZQUNmSSxzQkFBc0I7WUFDdEJLLFNBQVNrQyxPQUFPLEdBQUc7UUFDckI7SUFDRjtJQUVBLFVBQVU7SUFDVnZFLGdEQUFTQTtrQ0FBQztZQUNSLElBQUlJLFFBQVE7Z0JBQ1Ysd0RBQXdEO2dCQUN4RGtFO1lBQ0YsT0FBTztnQkFDTCw4Q0FBOEM7Z0JBQzlDaEM7WUFDRjtRQUNGO2lDQUFHO1FBQUNsQztLQUFPO0lBRVgsMkNBQTJDO0lBQzNDSixnREFBU0E7a0NBQUM7WUFDUixJQUFJLENBQUNJLFFBQVE7Z0JBQ1gsTUFBTW9ILFdBQVdDO3VEQUFZO3dCQUMzQm5GO29CQUNGO3NEQUFHO2dCQUVIOzhDQUFPLElBQU1vRixjQUFjRjs7WUFDN0I7UUFDRjtpQ0FBRztRQUFDcEg7S0FBTztJQUVYLGtDQUFrQztJQUNsQyxNQUFNdUgsZUFBZSxDQUFDbEU7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUNFLDhEQUFDbUU7b0JBQUlDLFdBQVU7b0JBQXlCQyxNQUFLO29CQUFPQyxRQUFPO29CQUFlQyxTQUFROzhCQUNoRiw0RUFBQ0M7d0JBQUtDLGVBQWM7d0JBQVFDLGdCQUFlO3dCQUFRQyxhQUFhO3dCQUFHQyxHQUFFOzs7Ozs7Ozs7OztZQUczRSxLQUFLO2dCQUNILHFCQUNFLDhEQUFDVDtvQkFBSUMsV0FBVTtvQkFBdUJDLE1BQUs7b0JBQU9DLFFBQU87b0JBQWVDLFNBQVE7OEJBQzlFLDRFQUFDQzt3QkFBS0MsZUFBYzt3QkFBUUMsZ0JBQWU7d0JBQVFDLGFBQWE7d0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1lBRzNFO2dCQUNFLHFCQUNFLDhEQUFDVDtvQkFBSUMsV0FBVTtvQkFBeUJDLE1BQUs7b0JBQU9DLFFBQU87b0JBQWVDLFNBQVE7OEJBQ2hGLDRFQUFDQzt3QkFBS0MsZUFBYzt3QkFBUUMsZ0JBQWU7d0JBQVFDLGFBQWE7d0JBQUdDLEdBQUU7Ozs7Ozs7Ozs7O1FBRzdFO0lBQ0Y7SUFFQSxNQUFNQyxpQkFBaUIsQ0FBQzdFO1FBQ3RCLE9BQVFBO1lBQ04sS0FBSztnQkFBTSxPQUFPO1lBQ2xCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNOEUsdUJBQXVCLENBQUNoRDtRQUM1QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBVSxPQUFPO1lBQ3RCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1pRCxnQkFBZ0IsQ0FBQ2pEO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBUSxPQUFPO1lBQ3BCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLDJDQUEyQztJQUMzQyxJQUFJbkYsUUFBUTtRQUNWLHFCQUNFLDhEQUFDcUk7WUFBSVosV0FBVTs7OEJBR2IsOERBQUNZO29CQUFJWixXQUFVOzhCQUNiLDRFQUFDYTt3QkFBR2IsV0FBVTtrQ0FBNkc7Ozs7Ozs7Ozs7OzhCQU03SCw4REFBQ1k7b0JBQUlaLFdBQVU7OEJBQ2IsNEVBQUNZO3dCQUFJWixXQUFVOzswQ0FFYiw4REFBQ1k7MENBQ0MsNEVBQUNBO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFJWixXQUFXLHdCQUdkaEgsT0FGQUEsbUJBQW1CLGFBQWEsZ0JBQWdCMEgscUJBQXFCMUgsaUJBQ3RFLHVEQUVBLE9BRENBLG1CQUFtQixhQUFhLEtBQUs7Ozs7Ozt3REFFdENBLG1CQUFtQiw0QkFDbEIsOERBQUM0SDs0REFBSVosV0FBVyx5Q0FBOEUsT0FBckNVLHFCQUFxQjFILGlCQUFnQjs7Ozs7Ozs7Ozs7OzhEQUdsRyw4REFBQzhIO29EQUFLZCxXQUFXLHVCQUVoQixPQURDaEgsbUJBQW1CLGFBQWEsa0JBQWtCLHdDQUNuRDs4REFBcUI7Ozs7Ozs7Ozs7OztzREFJeEIsOERBQUM4SDs0Q0FBS2QsV0FBVyxtREFLaEIsT0FKQ2hILG1CQUFtQixhQUFhLGtCQUNoQ0EsbUJBQW1CLFNBQVMsbUJBQzVCQSxtQkFBbUIsWUFBWSxvQkFDL0I7c0RBRUNBLG1CQUFtQixhQUFhLFFBQVEySCxjQUFjM0g7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU03RCw4REFBQzRIOzBDQUNDLDRFQUFDQTtvQ0FBSVosV0FBVTs7c0RBQ2IsOERBQUNZOzRDQUFJWixXQUFVOzs4REFDYiw4REFBQ1k7b0RBQUlaLFdBQVU7O3NFQUNiLDhEQUFDWTs0REFBSVosV0FBVyx3QkFHZDlHLE9BRkFBLGtCQUFrQixhQUFhLGdCQUFnQndILHFCQUFxQnhILGdCQUNyRSx1REFFQSxPQURDQSxrQkFBa0IsYUFBYSxLQUFLOzs7Ozs7d0RBRXJDQSxrQkFBa0IsNEJBQ2pCLDhEQUFDMEg7NERBQUlaLFdBQVcseUNBQTZFLE9BQXBDVSxxQkFBcUJ4SCxnQkFBZTs7Ozs7Ozs7Ozs7OzhEQUdqRyw4REFBQzRIO29EQUFLZCxXQUFXLHVCQUVoQixPQURDOUcsa0JBQWtCLGFBQWEsa0JBQWtCLHdDQUNsRDs4REFBcUI7Ozs7Ozs7Ozs7OztzREFJeEIsOERBQUM0SDs0Q0FBS2QsV0FBVyxtREFLaEIsT0FKQzlHLGtCQUFrQixhQUFhLGtCQUMvQkEsa0JBQWtCLFNBQVMsbUJBQzNCQSxrQkFBa0IsWUFBWSxvQkFDOUI7c0RBRUNBLGtCQUFrQixhQUFhLFFBQVF5SCxjQUFjekg7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU01RCw4REFBQzBIOzBDQUNDLDRFQUFDQTtvQ0FBSVosV0FBVTs7c0RBQ2IsOERBQUNZOzRDQUFJWixXQUFVOzs4REFDYiw4REFBQ1k7b0RBQUlaLFdBQVU7O3NFQUNiLDhEQUFDWTs0REFBSVosV0FBVyx3QkFHZDVHLE9BRkFBLGdCQUFnQixhQUFhLGdCQUFnQnNILHFCQUFxQnRILGNBQ25FLHVEQUVBLE9BRENBLGdCQUFnQixhQUFhLEtBQUs7Ozs7Ozt3REFFbkNBLGdCQUFnQiw0QkFDZiw4REFBQ3dIOzREQUFJWixXQUFXLHlDQUEyRSxPQUFsQ1UscUJBQXFCdEgsY0FBYTs7Ozs7Ozs7Ozs7OzhEQUcvRiw4REFBQzBIO29EQUFLZCxXQUFXLHVCQUVoQixPQURDNUcsZ0JBQWdCLGFBQWEsa0JBQWtCLHdDQUNoRDs4REFBcUI7Ozs7Ozs7Ozs7OztzREFJeEIsOERBQUMwSDs0Q0FBS2QsV0FBVyxtREFLaEIsT0FKQzVHLGdCQUFnQixhQUFhLGtCQUM3QkEsZ0JBQWdCLFNBQVMsbUJBQ3pCQSxnQkFBZ0IsWUFBWSxvQkFDNUI7c0RBRUNBLGdCQUFnQixhQUFhLFFBQVF1SCxjQUFjdkg7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU0xRCw4REFBQ3dIOzBDQUNDLDRFQUFDQTtvQ0FBSVosV0FBVTs7c0RBQ2IsOERBQUNZOzRDQUFJWixXQUFVOzs4REFDYiw4REFBQ1k7b0RBQUlaLFdBQVU7O3NFQUNiLDhEQUFDWTs0REFBSVosV0FBVyx3QkFHZDFHLE9BRkFBLGNBQWMsYUFBYSxnQkFBZ0JvSCxxQkFBcUJwSCxZQUNqRSx1REFFQSxPQURDQSxjQUFjLGFBQWEsS0FBSzs7Ozs7O3dEQUVqQ0EsY0FBYyw0QkFDYiw4REFBQ3NIOzREQUFJWixXQUFXLHlDQUF5RSxPQUFoQ1UscUJBQXFCcEgsWUFBVzs7Ozs7Ozs7Ozs7OzhEQUc3Riw4REFBQ3dIO29EQUFLZCxXQUFXLHVCQUVoQixPQURDMUcsY0FBYyxhQUFhLGtCQUFrQix3Q0FDOUM7OERBQXFCOzs7Ozs7Ozs7Ozs7c0RBSXhCLDhEQUFDd0g7NENBQUtkLFdBQVcsbURBS2hCLE9BSkMxRyxjQUFjLGFBQWEsa0JBQzNCQSxjQUFjLFNBQVMsbUJBQ3ZCQSxjQUFjLFlBQVksb0JBQzFCO3NEQUVDQSxjQUFjLGFBQWEsUUFBUXFILGNBQWNySDs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTXhELDhEQUFDc0g7MENBQ0MsNEVBQUNBO29DQUFJWixXQUFVOztzREFDYiw4REFBQ1k7NENBQUlaLFdBQVU7OzhEQUNiLDhEQUFDWTtvREFBSVosV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFJWixXQUFXLHdCQUdkeEcsT0FGQUEsZ0JBQWdCLGFBQWEsZ0JBQWdCa0gscUJBQXFCbEgsY0FDbkUsdURBRUEsT0FEQ0EsZ0JBQWdCLGFBQWEsS0FBSzs7Ozs7O3dEQUVuQ0EsZ0JBQWdCLDRCQUNmLDhEQUFDb0g7NERBQUlaLFdBQVcseUNBQTJFLE9BQWxDVSxxQkFBcUJsSCxjQUFhOzs7Ozs7d0RBRzVGTSw2QkFDQyw4REFBQzhHOzREQUFJWixXQUFVOzs7Ozs7Ozs7Ozs7OERBR25CLDhEQUFDYztvREFBS2QsV0FBVyx1QkFFaEIsT0FEQ3hHLGdCQUFnQixhQUFhLGtCQUFrQix3Q0FDaEQ7OERBQ0VNLGNBQWMsb0JBQW9COzs7Ozs7Ozs7Ozs7c0RBR3ZDLDhEQUFDZ0g7NENBQUtkLFdBQVcsbURBS2hCLE9BSkN4RyxnQkFBZ0IsYUFBYSxrQkFDN0JBLGdCQUFnQixTQUFTLG1CQUN6QkEsZ0JBQWdCLFlBQVksb0JBQzVCO3NEQUVDTSxjQUFjLFdBQVdOLGdCQUFnQixhQUFhLFFBQVFtSCxjQUFjbkg7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBWXRGSSwrQkFDQyw4REFBQ2dIO29CQUFJWixXQUFVOzhCQUNiLDRFQUFDWTt3QkFBSVosV0FBVTs7MENBQ2IsOERBQUNZO2dDQUFJWixXQUFVOzs7Ozs7MENBQ2YsOERBQUNjO2dDQUFLZCxXQUFVOzBDQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBUTlDOUYsb0NBQ0MsOERBQUMwRztvQkFBSVosV0FBVTs4QkFDYiw0RUFBQ1k7d0JBQUlaLFdBQVU7a0NBQXNDOzs7Ozs7Ozs7OztnQkFPeERoRyxpQ0FDQyw4REFBQzRHO29CQUFJWixXQUFVOzhCQUNiLDRFQUFDWTt3QkFBSVosV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVO2dDQUFVQyxNQUFLO2dDQUFPQyxRQUFPO2dDQUFlQyxTQUFROzBDQUNqRSw0RUFBQ0M7b0NBQUtDLGVBQWM7b0NBQVFDLGdCQUFlO29DQUFRQyxhQUFhO29DQUFHQyxHQUFFOzs7Ozs7Ozs7OzswQ0FFdkUsOERBQUNNOztvQ0FBSztvQ0FBSzlHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0JBTWhCQSxtQkFBbUIsQ0FBQ04saUJBQWlCLENBQUNFLCtCQUNyQyw4REFBQ2dIO29CQUFJWixXQUFVOzhCQUNiLDRFQUFDZTt3QkFDQ0MsU0FBU3ZFO3dCQUNUdUQsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFTWDtJQUVBLDZDQUE2QztJQUM3QyxxQkFDRSw4REFBQ1k7UUFBSVosV0FBVTs7MEJBRWIsOERBQUNZO2dCQUFJWixXQUFVOztrQ0FDYiw4REFBQ2lCO3dCQUFHakIsV0FBVTtrQ0FBK0I7Ozs7OztrQ0FDN0MsOERBQUNZO3dCQUFJWixXQUFXLHNDQUFpRixPQUEzQ3BILFVBQVUsa0JBQWtCOzs7Ozs7Ozs7Ozs7MEJBSXBGLDhEQUFDZ0k7Z0JBQUlaLFdBQVU7MEJBQ1pwSCxXQUFXRixXQUFXd0ksTUFBTSxLQUFLLElBQ2hDLFVBQVU7Z0JBQ1ZDLE1BQU1DLElBQUksQ0FBQztvQkFBRUYsUUFBUTtnQkFBRSxHQUFHakcsR0FBRyxDQUFDLENBQUNvRyxHQUFHQyxzQkFDaEMsOERBQUNWO3dCQUFnQlosV0FBVTs7MENBQ3pCLDhEQUFDWTtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7a0RBQ2YsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7Ozs7Ozs7MENBRWpCLDhEQUFDWTtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7a0RBQ2YsOERBQUNZO3dDQUFJWixXQUFVOzs7Ozs7Ozs7Ozs7O3VCQVBUc0I7Ozs7cUNBWVo1SSxXQUFXdUMsR0FBRyxDQUFDLENBQUNzRyxXQUFXRCxzQkFDM0IsOERBQUNWO3dCQUVDWixXQUFVOzswQ0FFViw4REFBQ1k7Z0NBQUlaLFdBQVU7O2tEQUNiLDhEQUFDYzt3Q0FBS2QsV0FBVTtrREFBc0N1QixVQUFVcEcsS0FBSzs7Ozs7O29DQUNwRTJFLGFBQWF5QixVQUFVM0YsS0FBSzs7Ozs7OzswQ0FFL0IsOERBQUNnRjtnQ0FBSVosV0FBVTs7a0RBQ2IsOERBQUNjO3dDQUFLZCxXQUFVO2tEQUF3Q3VCLFVBQVVuRyxLQUFLOzs7Ozs7a0RBQ3ZFLDhEQUFDMEY7d0NBQUtkLFdBQVcsdUJBQXVELE9BQWhDUyxlQUFlYyxVQUFVM0YsS0FBSztrREFDbkUyRixVQUFVOUYsTUFBTTs7Ozs7Ozs7Ozs7Ozt1QkFWaEI2Rjs7Ozs7Ozs7OzswQkFtQlgsOERBQUNWO2dCQUFJWixXQUFVOzBCQUNiLDRFQUFDWTtvQkFBSVosV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzRCQUFVQyxNQUFLOzRCQUFPQyxRQUFPOzRCQUFlQyxTQUFRO3NDQUNqRSw0RUFBQ0M7Z0NBQUtDLGVBQWM7Z0NBQVFDLGdCQUFlO2dDQUFRQyxhQUFhO2dDQUFHQyxHQUFFOzs7Ozs7Ozs7OztzQ0FFdkUsOERBQUNNO3NDQUNFbEksVUFBVSxjQUNWRSxhQUFhLGFBQXdCLE9BQVhBLGNBQzFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iO0dBM3FCTVI7S0FBQUE7QUE2cUJOLGlFQUFlQSxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbmc3XFxEZXNrdG9wXFxob21lXFx1YnVudHVcXGZpbmFuY2lhbF9kYXNoYm9hcmRcXHNyY1xcY29tcG9uZW50c1xcU3BlZWRUcmFmZmljLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0Q29tcGFueU5hbWUgfSBmcm9tICcuLi91dGlscy9jb21wYW55TG9va3VwJztcblxuaW50ZXJmYWNlIE1hcmtldEluZGljYXRvciB7XG4gIGxhYmVsOiBzdHJpbmc7XG4gIHZhbHVlOiBzdHJpbmc7XG4gIGNoYW5nZTogc3RyaW5nO1xuICB0cmVuZDogJ3VwJyB8ICdkb3duJyB8ICduZXV0cmFsJztcbiAgY29sb3I6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFNwZWVkVHJhZmZpY1Byb3BzIHtcbiAgc3ltYm9sPzogc3RyaW5nO1xuICBvblBoYXNlTWVzc2FnZT86IChtZXNzYWdlOiBzdHJpbmcsIGhhc1JlcG9ydEJ1dHRvbj86IGJvb2xlYW4pID0+IHZvaWQ7XG4gIG9uQW5hbHlzaXNDb21wbGV0ZT86IChyZXN1bHRzOiBBbmFseXNpc1Jlc3VsdHMpID0+IHZvaWQ7XG59XG5cbmludGVyZmFjZSBMU1RNUmVzdWx0IHtcbiAgc3ltYm9sOiBzdHJpbmc7XG4gIHRyYWluX3VudGlsOiBzdHJpbmc7XG4gIHByZWRpY3Rpb25zOiBBcnJheTx7XG4gICAgZGF0ZTogc3RyaW5nO1xuICAgIHByZWRfcHJvYl91cDogbnVtYmVyO1xuICAgIHByZWRpY3RlZF9sYWJlbDogbnVtYmVyO1xuICAgIGFjdHVhbF9sYWJlbDogbnVtYmVyO1xuICAgIHJlc3VsdF9jb2xvcjogc3RyaW5nO1xuICB9PjtcbiAgc3VtbWFyeV9rbz86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIE1GSVJlc3VsdCB7XG4gIHN5bWJvbDogc3RyaW5nO1xuICBkYXRlOiBzdHJpbmc7XG4gIG1maV8xNDogbnVtYmVyO1xuICB0cmFmZmljX2xpZ2h0OiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBQcmVkaWN0aW9uUmVzdWx0IHtcbiAgcGhhc2U/OiBudW1iZXI7XG4gIGxzdG0/OiBMU1RNUmVzdWx0O1xuICBtZmk/OiBNRklSZXN1bHQ7XG4gIGJvbGxpbmdlcj86IGFueTtcbiAgcnNpPzogYW55O1xuICBpbmR1c3RyeT86IGFueTtcbiAgY2FwbT86IGFueTtcbiAgZ2FyY2g/OiBhbnk7XG4gIHRyYWZmaWNfbGlnaHRzPzoge1xuICAgIHRlY2huaWNhbD86IHN0cmluZztcbiAgICBpbmR1c3RyeT86IHN0cmluZztcbiAgICBtYXJrZXQ/OiBzdHJpbmc7XG4gICAgcmlzaz86IHN0cmluZztcbiAgICBuZXVyYWw/OiBzdHJpbmc7XG4gIH07XG59XG5cbmludGVyZmFjZSBBbmFseXNpc1Jlc3VsdHMge1xuICBzeW1ib2w6IHN0cmluZztcbiAgY29tcGFueU5hbWU6IHN0cmluZztcbiAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIGFuYWx5c2lzRGF0ZTogc3RyaW5nO1xuICBsc3RtPzogTFNUTVJlc3VsdDtcbiAgbWZpPzogTUZJUmVzdWx0O1xuICBib2xsaW5nZXI/OiBhbnk7XG4gIHJzaT86IGFueTtcbiAgaW5kdXN0cnk/OiBhbnk7XG4gIGNhcG0/OiBhbnk7XG4gIGdhcmNoPzogYW55O1xuICB0cmFmZmljX2xpZ2h0czoge1xuICAgIHRlY2huaWNhbD86IHN0cmluZztcbiAgICBpbmR1c3RyeT86IHN0cmluZztcbiAgICBtYXJrZXQ/OiBzdHJpbmc7XG4gICAgcmlzaz86IHN0cmluZztcbiAgICBuZXVyYWw/OiBzdHJpbmc7XG4gIH07XG59XG5cbmNvbnN0IFNwZWVkVHJhZmZpYzogUmVhY3QuRkM8U3BlZWRUcmFmZmljUHJvcHM+ID0gKHsgc3ltYm9sLCBvblBoYXNlTWVzc2FnZSwgb25BbmFseXNpc0NvbXBsZXRlIH0pID0+IHtcbiAgLy8gTWFya2V0IGluZGljYXRvcnMgc3RhdGUgKFByZS10aWNrZXIgbW9kZSlcbiAgY29uc3QgW2luZGljYXRvcnMsIHNldEluZGljYXRvcnNdID0gdXNlU3RhdGU8TWFya2V0SW5kaWNhdG9yW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtsYXN0VXBkYXRlLCBzZXRMYXN0VXBkYXRlXSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gIC8vIFRyYWZmaWMgbGlnaHRzIHN0YXRlIChQb3N0LXRpY2tlciBtb2RlKSAtIE5ldyBvcmRlclxuICBjb25zdCBbdGVjaG5pY2FsTGlnaHQsIHNldFRlY2huaWNhbExpZ2h0XSA9IHVzZVN0YXRlPCdnb29kJyB8ICd3YXJuaW5nJyB8ICdkYW5nZXInIHwgJ2luYWN0aXZlJz4oJ2luYWN0aXZlJyk7IC8vIExpZ2h0IDE6IFRlY2huaWNhbCBBbmFseXNpc1xuICBjb25zdCBbaW5kdXN0cnlMaWdodCwgc2V0SW5kdXN0cnlMaWdodF0gPSB1c2VTdGF0ZTwnZ29vZCcgfCAnd2FybmluZycgfCAnZGFuZ2VyJyB8ICdpbmFjdGl2ZSc+KCdpbmFjdGl2ZScpOyAvLyBMaWdodCAyOiBJbmR1c3RyeSBTZW5zaXRpdml0eVxuICBjb25zdCBbbWFya2V0TGlnaHQsIHNldE1hcmtldExpZ2h0XSA9IHVzZVN0YXRlPCdnb29kJyB8ICd3YXJuaW5nJyB8ICdkYW5nZXInIHwgJ2luYWN0aXZlJz4oJ2luYWN0aXZlJyk7IC8vIExpZ2h0IDM6IE1hcmtldCBTZW5zaXRpdml0eSAoQ0FQTSlcbiAgY29uc3QgW3Jpc2tMaWdodCwgc2V0Umlza0xpZ2h0XSA9IHVzZVN0YXRlPCdnb29kJyB8ICd3YXJuaW5nJyB8ICdkYW5nZXInIHwgJ2luYWN0aXZlJz4oJ2luYWN0aXZlJyk7IC8vIExpZ2h0IDQ6IFZvbGF0aWxpdHkgUmlza1xuICBjb25zdCBbbmV1cmFsTGlnaHQsIHNldE5ldXJhbExpZ2h0XSA9IHVzZVN0YXRlPCdnb29kJyB8ICd3YXJuaW5nJyB8ICdkYW5nZXInIHwgJ2luYWN0aXZlJz4oJ2luYWN0aXZlJyk7IC8vIExpZ2h0IDU6IE5ldXJhbCBOZXR3b3JrIChMU1RNKVxuXG4gIC8vIFByZWRpY3Rpb24gc3RhdGVcbiAgY29uc3QgW3BoYXNlMUxvYWRpbmcsIHNldFBoYXNlMUxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcGhhc2UyTG9hZGluZywgc2V0UGhhc2UyTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsc3RtTG9hZGluZywgc2V0THN0bUxvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcHJlZGljdGlvbkVycm9yLCBzZXRQcmVkaWN0aW9uRXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzaG93VGltZW91dE1lc3NhZ2UsIHNldFNob3dUaW1lb3V0TWVzc2FnZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsYXN0UmVxdWVzdFRpbWUsIHNldExhc3RSZXF1ZXN0VGltZV0gPSB1c2VTdGF0ZTxudW1iZXI+KDApO1xuXG4gIC8vIFJlc3VsdHMgc3RvcmFnZSBzdGF0ZVxuICBjb25zdCBbYWxsUmVzdWx0cywgc2V0QWxsUmVzdWx0c10gPSB1c2VTdGF0ZTxQYXJ0aWFsPEFuYWx5c2lzUmVzdWx0cz4+KHt9KTtcblxuXG5cbiAgLy8gU2luZ2xlLWZsaWdodCBndWFyZFxuICBjb25zdCBpbkZsaWdodCA9IHVzZVJlZihmYWxzZSk7XG5cbiAgLy8g7Iuk7KCcIOyLnOyepSDrjbDsnbTthLAg6rCA7KC47Jik6riwIChQcmUtdGlja2VyIG1vZGUpXG4gIGNvbnN0IGZldGNoTWFya2V0RGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvbWFya2V0X2RhdGEnKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcblxuICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzICYmIHJlc3VsdC5kYXRhKSB7XG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZERhdGEgPSByZXN1bHQuZGF0YS5tYXAoKGl0ZW06IGFueSkgPT4gKHtcbiAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCxcbiAgICAgICAgICB2YWx1ZTogaXRlbS5wcmljZS50b0xvY2FsZVN0cmluZygnZW4tVVMnLCB7XG4gICAgICAgICAgICBtaW5pbXVtRnJhY3Rpb25EaWdpdHM6IDIsXG4gICAgICAgICAgICBtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDJcbiAgICAgICAgICB9KSxcbiAgICAgICAgICBjaGFuZ2U6IGAke2l0ZW0uY2hhbmdlUGVyY2VudCA+PSAwID8gJysnIDogJyd9JHtpdGVtLmNoYW5nZVBlcmNlbnQudG9GaXhlZCgyKX0lYCxcbiAgICAgICAgICB0cmVuZDogaXRlbS50cmVuZCxcbiAgICAgICAgICBjb2xvcjogaXRlbS50cmVuZCA9PT0gJ3VwJyA/ICdncmVlbicgOiBpdGVtLnRyZW5kID09PSAnZG93bicgPyAncmVkJyA6ICd5ZWxsb3cnXG4gICAgICAgIH0pKTtcblxuICAgICAgICBzZXRJbmRpY2F0b3JzKGZvcm1hdHRlZERhdGEpO1xuICAgICAgICBzZXRMYXN0VXBkYXRlKG5ldyBEYXRlKCkudG9Mb2NhbGVUaW1lU3RyaW5nKCdrby1LUicpKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGZldGNoIG1hcmtldCBkYXRhOicsIGVycm9yKTtcbiAgICAgIC8vIOyXkOufrCDsi5wgZmFsbGJhY2sg642w7J207YSwIOyCrOyaqVxuICAgICAgc2V0SW5kaWNhdG9ycyhbXG4gICAgICAgIHsgbGFiZWw6ICdTJlAgNTAwJywgdmFsdWU6ICc0LDU2Ny44OScsIGNoYW5nZTogJysxLjIlJywgdHJlbmQ6ICd1cCcsIGNvbG9yOiAnZ3JlZW4nIH0sXG4gICAgICAgIHsgbGFiZWw6ICfrgpjsiqTri6UnLCB2YWx1ZTogJzE0LDIzNC41NicsIGNoYW5nZTogJyswLjglJywgdHJlbmQ6ICd1cCcsIGNvbG9yOiAnZ3JlZW4nIH0sXG4gICAgICAgIHsgbGFiZWw6ICfri6TsmrDsobTsiqQnLCB2YWx1ZTogJzM0LDU2Ny4xMicsIGNoYW5nZTogJy0wLjMlJywgdHJlbmQ6ICdkb3duJywgY29sb3I6ICdyZWQnIH0sXG4gICAgICAgIHsgbGFiZWw6ICdWSVgnLCB2YWx1ZTogJzE4LjQ1JywgY2hhbmdlOiAnLTIuMSUnLCB0cmVuZDogJ2Rvd24nLCBjb2xvcjogJ3llbGxvdycgfSxcbiAgICAgICAgeyBsYWJlbDogJ+uLrOufrC/sm5AnLCB2YWx1ZTogJzEsMzI3LjUwJywgY2hhbmdlOiAnKzAuNSUnLCB0cmVuZDogJ3VwJywgY29sb3I6ICdncmVlbicgfSxcbiAgICAgIF0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gQ29udmVydCByZXN1bHQgY29sb3IgdG8gdHJhZmZpYyBsaWdodCBzdGF0dXNcbiAgY29uc3QgcmVzdWx0Q29sb3JUb1N0YXR1cyA9IChjb2xvcjogc3RyaW5nKTogJ2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5hY3RpdmUnID0+IHtcbiAgICBzd2l0Y2ggKGNvbG9yPy50b0xvd2VyQ2FzZSgpKSB7XG4gICAgICBjYXNlICdncmVlbic6XG4gICAgICBjYXNlICdnb29kJzpcbiAgICAgICAgcmV0dXJuICdnb29kJztcbiAgICAgIGNhc2UgJ3llbGxvdyc6XG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuICd3YXJuaW5nJztcbiAgICAgIGNhc2UgJ3JlZCc6XG4gICAgICBjYXNlICdkYW5nZXInOlxuICAgICAgICByZXR1cm4gJ2Rhbmdlcic7XG4gICAgICBjYXNlICdpbmFjdGl2ZSc6XG4gICAgICBjYXNlICdncmV5JzpcbiAgICAgIGNhc2UgJ2dyYXknOlxuICAgICAgICByZXR1cm4gJ2luYWN0aXZlJztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiAnd2FybmluZyc7XG4gICAgfVxuICB9O1xuXG4gIC8vIEV4dHJhY3QgY29sb3IgZnJvbSBMU1RNL01GSSByZXN1bHRzXG4gIGNvbnN0IGdldENvbG9yRnJvbVJlc3VsdCA9IChvYmo6IGFueSk6IHN0cmluZyB8IHVuZGVmaW5lZCA9PiB7XG4gICAgaWYgKCFvYmopIHJldHVybiB1bmRlZmluZWQ7XG4gICAgaWYgKHR5cGVvZiBvYmogPT09ICdzdHJpbmcnKSByZXR1cm4gb2JqO1xuICAgIGlmIChvYmoucmVzdWx0X2NvbG9yKSByZXR1cm4gb2JqLnJlc3VsdF9jb2xvcjtcbiAgICBpZiAob2JqLnRyYWZmaWNfbGlnaHQpIHJldHVybiBvYmoudHJhZmZpY19saWdodDtcbiAgICBpZiAob2JqLmNvbG9yKSByZXR1cm4gb2JqLmNvbG9yO1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH07XG5cbiAgLy8gU3RhZ2VkIGV4ZWN1dGlvbjogUGhhc2UgMSAoRmFzdCBzZXJ2aWNlcykgKyBQaGFzZSAyIChMU1RNKVxuICBjb25zdCBmZXRjaFN0YWdlZFByZWRpY3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzeW1ib2wgfHwgaW5GbGlnaHQuY3VycmVudCkgcmV0dXJuO1xuXG4gICAgLy8gUHJldmVudCB0b28gZnJlcXVlbnQgcmVxdWVzdHMgKG1pbmltdW0gMTAgc2Vjb25kcyBiZXR3ZWVuIHJlcXVlc3RzKVxuICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XG4gICAgaWYgKG5vdyAtIGxhc3RSZXF1ZXN0VGltZSA8IDEwMDAwKSB7XG4gICAgICBjb25zb2xlLmxvZygnUHJlZGljdGlvbiByZXF1ZXN0IHRocm90dGxlZCAtIHRvbyBmcmVxdWVudCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBTZXQgc2luZ2xlLWZsaWdodCBndWFyZFxuICAgICAgaW5GbGlnaHQuY3VycmVudCA9IHRydWU7XG4gICAgICBzZXRMYXN0UmVxdWVzdFRpbWUobm93KTtcblxuICAgICAgLy8gUmVzZXQgYWxsIGxpZ2h0cyB0byBpbmFjdGl2ZSBzdGF0ZVxuICAgICAgc2V0VGVjaG5pY2FsTGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRJbmR1c3RyeUxpZ2h0KCdpbmFjdGl2ZScpO1xuICAgICAgc2V0TWFya2V0TGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRSaXNrTGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXROZXVyYWxMaWdodCgnaW5hY3RpdmUnKTtcblxuICAgICAgc2V0UHJlZGljdGlvbkVycm9yKG51bGwpO1xuICAgICAgc2V0U2hvd1RpbWVvdXRNZXNzYWdlKGZhbHNlKTtcblxuICAgICAgLy8gR2V0IGNvbXBhbnkgbmFtZSBmb3IgdXNlciBmZWVkYmFja1xuICAgICAgY29uc3QgY29tcGFueU5hbWUgPSBnZXRDb21wYW55TmFtZShzeW1ib2wpO1xuXG4gICAgICAvLyBJbml0aWFsIHVzZXIgZmVlZGJhY2sgbWVzc2FnZVxuICAgICAgb25QaGFzZU1lc3NhZ2U/Lihg8J+agCAke2NvbXBhbnlOYW1lfSDssKjtirgg67aE7ISd7J2EIOyLnOyeke2VoOqyjOyalCEg8J+TimApO1xuXG4gICAgICAvLyBXYWl0IDEuNSBzZWNvbmRzIGJlZm9yZSBzdGFydGluZyBQaGFzZSAxXG4gICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTUwMCkpO1xuXG4gICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gU3RhcnRpbmcgc3RhZ2VkIHByZWRpY3Rpb24gZm9yICR7c3ltYm9sfWApO1xuXG4gICAgICAvLyBQaGFzZSAxOiBFeGVjdXRlIGZhc3Qgc2VydmljZXMgKFRlY2huaWNhbCwgSW5kdXN0cnksIE1hcmtldCwgVm9sYXRpbGl0eSlcbiAgICAgIHNldFBoYXNlMUxvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gUGhhc2UgMTogU3RhcnRpbmcgZmFzdCBzZXJ2aWNlcyBmb3IgJHtzeW1ib2x9YCk7XG5cbiAgICAgIGNvbnN0IHBoYXNlMVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvc3BlZWR0cmFmZmljX3N0YWdlZD9zeW1ib2w9JHtzeW1ib2x9JnN0YWdlPXBoYXNlMWAsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIHNpZ25hbDogQWJvcnRTaWduYWwudGltZW91dCgzMDAwMCksIC8vIDMwIHNlY29uZCB0aW1lb3V0IGZvciBmYXN0IHNlcnZpY2VzXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFwaGFzZTFSZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFBoYXNlIDEgSFRUUCAke3BoYXNlMVJlc3BvbnNlLnN0YXR1c306ICR7cGhhc2UxUmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcGhhc2UxUmVzdWx0ID0gYXdhaXQgcGhhc2UxUmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIFBoYXNlIDEgcmVzdWx0OmAsIHBoYXNlMVJlc3VsdCk7XG5cbiAgICAgIC8vIFVwZGF0ZSBsaWdodHMgMS00IGltbWVkaWF0ZWx5IGFmdGVyIFBoYXNlIDEgY29tcGxldGVzXG4gICAgICBpZiAocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzKSB7XG4gICAgICAgIGlmIChwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMudGVjaG5pY2FsKSB7XG4gICAgICAgICAgc2V0VGVjaG5pY2FsTGlnaHQocmVzdWx0Q29sb3JUb1N0YXR1cyhwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMudGVjaG5pY2FsKSk7XG4gICAgICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIFRlY2huaWNhbCBsaWdodCBzZXQgdG86ICR7cGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLnRlY2huaWNhbH1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLmluZHVzdHJ5KSB7XG4gICAgICAgICAgc2V0SW5kdXN0cnlMaWdodChyZXN1bHRDb2xvclRvU3RhdHVzKHBoYXNlMVJlc3VsdC50cmFmZmljX2xpZ2h0cy5pbmR1c3RyeSkpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBJbmR1c3RyeSBsaWdodCBzZXQgdG86ICR7cGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLmluZHVzdHJ5fWApO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMubWFya2V0KSB7XG4gICAgICAgICAgc2V0TWFya2V0TGlnaHQocmVzdWx0Q29sb3JUb1N0YXR1cyhwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMubWFya2V0KSk7XG4gICAgICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIE1hcmtldCBsaWdodCBzZXQgdG86ICR7cGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLm1hcmtldH1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzLnJpc2spIHtcbiAgICAgICAgICBzZXRSaXNrTGlnaHQocmVzdWx0Q29sb3JUb1N0YXR1cyhwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMucmlzaykpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBSaXNrIGxpZ2h0IHNldCB0bzogJHtwaGFzZTFSZXN1bHQudHJhZmZpY19saWdodHMucmlza31gKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBTdG9yZSBQaGFzZSAxIHJlc3VsdHNcbiAgICAgIHNldEFsbFJlc3VsdHMocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBzeW1ib2wsXG4gICAgICAgIG1maTogcGhhc2UxUmVzdWx0Lm1maSxcbiAgICAgICAgYm9sbGluZ2VyOiBwaGFzZTFSZXN1bHQuYm9sbGluZ2VyLFxuICAgICAgICByc2k6IHBoYXNlMVJlc3VsdC5yc2ksXG4gICAgICAgIGluZHVzdHJ5OiBwaGFzZTFSZXN1bHQuaW5kdXN0cnksXG4gICAgICAgIGNhcG06IHBoYXNlMVJlc3VsdC5jYXBtLFxuICAgICAgICBnYXJjaDogcGhhc2UxUmVzdWx0LmdhcmNoLFxuICAgICAgICB0cmFmZmljX2xpZ2h0czoge1xuICAgICAgICAgIC4uLnByZXYudHJhZmZpY19saWdodHMsXG4gICAgICAgICAgLi4ucGhhc2UxUmVzdWx0LnRyYWZmaWNfbGlnaHRzXG4gICAgICAgIH1cbiAgICAgIH0pKTtcblxuICAgICAgc2V0UGhhc2UxTG9hZGluZyhmYWxzZSk7XG4gICAgICBjb25zb2xlLmxvZyhgW1NwZWVkVHJhZmZpY10gUGhhc2UgMSBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5IGZvciAke3N5bWJvbH1gKTtcblxuICAgICAgLy8gU2VuZCBQaGFzZSAxIGNvbXBsZXRpb24gbWVzc2FnZSB0byBjaGF0XG4gICAgICBvblBoYXNlTWVzc2FnZT8uKCfquLDsiKDsoIEg67aE7ISdLCDsgrDsl4Ug66+86rCQ64+ELCDsi5zsnqUg66+86rCQ64+ELCDrs4Drj5nshLEg66as7Iqk7YGsIOu2hOyEneydhCDrp4jss6TslrTsmpQhIPCfk4onKTtcblxuICAgICAgLy8gV2FpdCAxLjUgc2Vjb25kcyBiZWZvcmUgc3RhcnRpbmcgUGhhc2UgMlxuICAgICAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDE1MDApKTtcblxuICAgICAgLy8gU2VuZCBQaGFzZSAyIGVudHJ5IG1lc3NhZ2UgdG8gY2hhdFxuICAgICAgb25QaGFzZU1lc3NhZ2U/Lign7J207KCcIOuUpeufrOuLnSDquLDrsJgg6rCA6rKpIOuzgOuPmSDsmIjsuKHsnYQg7KeE7ZaJ7ZWp64uI64ukLiDsnqDsi5zrp4wg6riw64uk66CkIOyjvOyEuOyalCEg8J+klicpO1xuXG4gICAgICAvLyBQaGFzZSAyOiBFeGVjdXRlIExTVE0gc2VydmljZVxuICAgICAgc2V0UGhhc2UyTG9hZGluZyh0cnVlKTtcbiAgICAgIHNldExzdG1Mb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIFBoYXNlIDI6IFN0YXJ0aW5nIExTVE0gc2VydmljZSBmb3IgJHtzeW1ib2x9YCk7XG5cbiAgICAgIC8vIFN0YXJ0IDIwLXNlY29uZCB0aW1lciBmb3IgS29yZWFuIHRpbWVvdXQgbWVzc2FnZVxuICAgICAgY29uc3QgdGltZW91dFRpbWVyID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNldFNob3dUaW1lb3V0TWVzc2FnZSh0cnVlKTtcbiAgICAgIH0sIDIwMDAwKTtcblxuICAgICAgY29uc3QgcGhhc2UyUmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9zcGVlZHRyYWZmaWNfc3RhZ2VkP3N5bWJvbD0ke3N5bWJvbH0mc3RhZ2U9cGhhc2UyYCwge1xuICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgc2lnbmFsOiBBYm9ydFNpZ25hbC50aW1lb3V0KDYwMDAwKSwgLy8gNjAgc2Vjb25kIHRpbWVvdXQgZm9yIExTVE1cbiAgICAgIH0pO1xuXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dFRpbWVyKTtcblxuICAgICAgaWYgKCFwaGFzZTJSZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFBoYXNlIDIgSFRUUCAke3BoYXNlMlJlc3BvbnNlLnN0YXR1c306ICR7cGhhc2UyUmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcGhhc2UyUmVzdWx0ID0gYXdhaXQgcGhhc2UyUmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIFBoYXNlIDIgcmVzdWx0OmAsIHBoYXNlMlJlc3VsdCk7XG5cbiAgICAgIC8vIFVwZGF0ZSBsaWdodCA1IGFmdGVyIFBoYXNlIDIgY29tcGxldGVzXG4gICAgICBpZiAocGhhc2UyUmVzdWx0LnRyYWZmaWNfbGlnaHRzICYmIHBoYXNlMlJlc3VsdC50cmFmZmljX2xpZ2h0cy5uZXVyYWwpIHtcbiAgICAgICAgc2V0TmV1cmFsTGlnaHQocmVzdWx0Q29sb3JUb1N0YXR1cyhwaGFzZTJSZXN1bHQudHJhZmZpY19saWdodHMubmV1cmFsKSk7XG4gICAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBOZXVyYWwgbGlnaHQgc2V0IHRvOiAke3BoYXNlMlJlc3VsdC50cmFmZmljX2xpZ2h0cy5uZXVyYWx9YCk7XG4gICAgICB9XG5cbiAgICAgIC8vIFN0b3JlIFBoYXNlIDIgcmVzdWx0cyBhbmQgc2F2ZSBjb21wbGV0ZSBhbmFseXNpc1xuICAgICAgY29uc3QgZmluYWxSZXN1bHRzOiBBbmFseXNpc1Jlc3VsdHMgPSB7XG4gICAgICAgIHN5bWJvbCxcbiAgICAgICAgY29tcGFueU5hbWU6IGdldENvbXBhbnlOYW1lKHN5bWJvbCksXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICBhbmFseXNpc0RhdGU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgICBsc3RtOiBwaGFzZTJSZXN1bHQubHN0bSxcbiAgICAgICAgbWZpOiBhbGxSZXN1bHRzLm1maSxcbiAgICAgICAgYm9sbGluZ2VyOiBhbGxSZXN1bHRzLmJvbGxpbmdlcixcbiAgICAgICAgcnNpOiBhbGxSZXN1bHRzLnJzaSxcbiAgICAgICAgaW5kdXN0cnk6IGFsbFJlc3VsdHMuaW5kdXN0cnksXG4gICAgICAgIGNhcG06IGFsbFJlc3VsdHMuY2FwbSxcbiAgICAgICAgZ2FyY2g6IGFsbFJlc3VsdHMuZ2FyY2gsXG4gICAgICAgIHRyYWZmaWNfbGlnaHRzOiB7XG4gICAgICAgICAgLi4uYWxsUmVzdWx0cy50cmFmZmljX2xpZ2h0cyxcbiAgICAgICAgICBuZXVyYWw6IHBoYXNlMlJlc3VsdC50cmFmZmljX2xpZ2h0cz8ubmV1cmFsXG4gICAgICAgIH1cbiAgICAgIH07XG5cbiAgICAgIC8vIFNhdmUgY29tcHJlaGVuc2l2ZSByZXN1bHRzIHRvIGZpbGUgdmlhIEFQSVxuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2F2ZVJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvc2F2ZV9hbmFseXNpc19yZXN1bHRzJywge1xuICAgICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgfSxcbiAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShmaW5hbFJlc3VsdHMpLFxuICAgICAgICB9KTtcblxuICAgICAgICBpZiAoc2F2ZVJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgY29uc3Qgc2F2ZVJlc3VsdCA9IGF3YWl0IHNhdmVSZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgY29uc29sZS5sb2coYFtTcGVlZFRyYWZmaWNdIEFuYWx5c2lzIHJlc3VsdHMgc2F2ZWQgZm9yICR7c3ltYm9sfTpgLCBzYXZlUmVzdWx0LmZpbGVwYXRoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGBbU3BlZWRUcmFmZmljXSBGYWlsZWQgdG8gc2F2ZSByZXN1bHRzIGZvciAke3N5bWJvbH06YCwgYXdhaXQgc2F2ZVJlc3BvbnNlLnRleHQoKSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYFtTcGVlZFRyYWZmaWNdIEVycm9yIHNhdmluZyByZXN1bHRzIGZvciAke3N5bWJvbH06YCwgZXJyb3IpO1xuICAgICAgfVxuXG4gICAgICBzZXRQaGFzZTJMb2FkaW5nKGZhbHNlKTtcbiAgICAgIHNldExzdG1Mb2FkaW5nKGZhbHNlKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBQaGFzZSAyIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkgZm9yICR7c3ltYm9sfWApO1xuXG4gICAgICAvLyBOb3RpZnkgcGFyZW50IGNvbXBvbmVudCBvZiBhbmFseXNpcyBjb21wbGV0aW9uXG4gICAgICBvbkFuYWx5c2lzQ29tcGxldGU/LihmaW5hbFJlc3VsdHMpO1xuXG4gICAgICAvLyBTZW5kIFBoYXNlIDIgY29tcGxldGlvbiBtZXNzYWdlIHRvIGNoYXRcbiAgICAgIG9uUGhhc2VNZXNzYWdlPy4oJ+uqqOuToCDrtoTshJ3snbQg7JmE66OM65CY7JeI7Iq164uI64ukISDqsrDqs7zrpbwg7ZmV7J247ZW0IOuztOyEuOyalC4g4pyoJyk7XG5cbiAgICAgIC8vIFdhaXQgMS41IHNlY29uZHMsIHRoZW4gZGlzcGxheSByZXBvcnQgcHJvbXB0XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgb25QaGFzZU1lc3NhZ2U/Lign66qo65OgIOuNsOydtO2EsOqwgCDspIDruYTrkJjsl4jrhKTsmpQuIFNwZWVkVHJhZmZpYyDrtoTshJ0g67O06rOg7ISc6rCAIO2VhOyalO2VmOyLoOqwgOyalD8g8J+TiicsIHRydWUpO1xuICAgICAgfSwgMTUwMCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGBbU3BlZWRUcmFmZmljXSBBbGwgcGhhc2VzIGNvbXBsZXRlZCBzdWNjZXNzZnVsbHkgZm9yICR7c3ltYm9sfWApO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1N0YWdlZCBwcmVkaWN0aW9uIGVycm9yOicsIGVycm9yKTtcblxuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yLm5hbWUgPT09ICdUaW1lb3V0RXJyb3InKSB7XG4gICAgICAgICAgc2V0UHJlZGljdGlvbkVycm9yKCfsmpTssq0g7Iuc6rCEIOy0iOqzvCcpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNldFByZWRpY3Rpb25FcnJvcihg7JiI7LihIOyLpO2MqDogJHtlcnJvci5tZXNzYWdlfWApO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRQcmVkaWN0aW9uRXJyb3IoJ+yYiOy4oSDshJzruYTsiqQg7Jew6rKwIOyLpO2MqCcpO1xuICAgICAgfVxuXG4gICAgICAvLyBSZXNldCBhbGwgbGlnaHRzIHRvIGluYWN0aXZlIG9uIGVycm9yXG4gICAgICBzZXRUZWNobmljYWxMaWdodCgnaW5hY3RpdmUnKTtcbiAgICAgIHNldEluZHVzdHJ5TGlnaHQoJ2luYWN0aXZlJyk7XG4gICAgICBzZXRNYXJrZXRMaWdodCgnaW5hY3RpdmUnKTtcbiAgICAgIHNldFJpc2tMaWdodCgnaW5hY3RpdmUnKTtcbiAgICAgIHNldE5ldXJhbExpZ2h0KCdpbmFjdGl2ZScpO1xuXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFBoYXNlMUxvYWRpbmcoZmFsc2UpO1xuICAgICAgc2V0UGhhc2UyTG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRMc3RtTG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRTaG93VGltZW91dE1lc3NhZ2UoZmFsc2UpO1xuICAgICAgaW5GbGlnaHQuY3VycmVudCA9IGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICAvLyBFZmZlY3RzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHN5bWJvbCkge1xuICAgICAgLy8gV2hlbiBzeW1ib2wgaXMgcHJvdmlkZWQsIGZldGNoIHN0YWdlZCBwcmVkaWN0aW9uIG9uY2VcbiAgICAgIGZldGNoU3RhZ2VkUHJlZGljdGlvbigpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBXaGVuIG5vIHN5bWJvbCwgZmV0Y2ggbWFya2V0IGRhdGEgaW5pdGlhbGx5XG4gICAgICBmZXRjaE1hcmtldERhdGEoKTtcbiAgICB9XG4gIH0sIFtzeW1ib2xdKTtcblxuICAvLyAyMOy0iOuniOuLpCDsi5zsnqUg642w7J207YSwIOyXheuNsOydtO2KuCAoUHJlLXRpY2tlciBtb2RlIG9ubHkpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFzeW1ib2wpIHtcbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgICBmZXRjaE1hcmtldERhdGEoKTtcbiAgICAgIH0sIDIwMDAwKTtcblxuICAgICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICAgIH1cbiAgfSwgW3N5bWJvbF0pO1xuXG4gIC8vIFV0aWxpdHkgZnVuY3Rpb25zIGZvciByZW5kZXJpbmdcbiAgY29uc3QgZ2V0VHJlbmRJY29uID0gKHRyZW5kOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHRyZW5kKSB7XG4gICAgICBjYXNlICd1cCc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtZ3JlZW4tNTAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNyAxN2w5LjItOS4yTTE3IDE3VjdIN1wiIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICk7XG4gICAgICBjYXNlICdkb3duJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNTAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTcgN2wtOS4yIDkuMk03IDd2MTBoMTBcIiAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICApO1xuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1zbGF0ZS00MDBcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0yMCAxMkg0XCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0Q2hhbmdlQ29sb3IgPSAodHJlbmQ6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAodHJlbmQpIHtcbiAgICAgIGNhc2UgJ3VwJzogcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCc7XG4gICAgICBjYXNlICdkb3duJzogcmV0dXJuICd0ZXh0LXJlZC02MDAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICd0ZXh0LXNsYXRlLTUwMCc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGdldFRyYWZmaWNMaWdodENvbG9yID0gKHN0YXR1czogJ2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAnZ29vZCc6IHJldHVybiAnYmctZ3JlZW4tNTAwJztcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOiByZXR1cm4gJ2JnLXllbGxvdy01MDAnO1xuICAgICAgY2FzZSAnZGFuZ2VyJzogcmV0dXJuICdiZy1yZWQtNTAwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnYmctZ3JheS00MDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNUZXh0ID0gKHN0YXR1czogJ2dvb2QnIHwgJ3dhcm5pbmcnIHwgJ2RhbmdlcicgfCAnaW5hY3RpdmUnKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ2dvb2QnOiByZXR1cm4gJ+yWke2YuCc7XG4gICAgICBjYXNlICd3YXJuaW5nJzogcmV0dXJuICfrs7TthrUnO1xuICAgICAgY2FzZSAnZGFuZ2VyJzogcmV0dXJuICfso7zsnZgnO1xuICAgICAgY2FzZSAnaW5hY3RpdmUnOiByZXR1cm4gJ+u5hO2ZnOyEse2ZlCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ+u2hOyEneykkSc7XG4gICAgfVxuICB9O1xuXG4gIC8vIFBvc3QtdGlja2VyIG1vZGU6IFRyYWZmaWMgbGlnaHRzIGRpc3BsYXlcbiAgaWYgKHN5bWJvbCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBtYXgtdy1mdWxsIG92ZXJmbG93LWhpZGRlblwiPlxuXG4gICAgICAgIHsvKiBTcGVlZFRyYWZmaWPihKIg67iM656c65SpICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtdHJhbnNwYXJlbnQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNDAwIHZpYS1wdXJwbGUtNTAwIHRvLWJsdWUtNjAwIGJnLWNsaXAtdGV4dFwiPlxuICAgICAgICAgICAgU3BlZWRUcmFmZmlj4oSiXG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIDUtTGlnaHQg7Iug7Zi465OxIOyLnOyKpO2FnCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1iciBmcm9tLXNsYXRlLTgwMCB0by1zbGF0ZS05MDAgcm91bmRlZC0yeGwgcC03IHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLXNsYXRlLTcwMCBtaW4taC1bMzIwcHhdXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTVcIj5cbiAgICAgICAgICAgIHsvKiBMaWdodCAxOiBUZWNobmljYWwgQW5hbHlzaXMgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBncm91cFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC01XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtcbiAgICAgICAgICAgICAgICAgICAgICB0ZWNobmljYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICdiZy1ncmF5LTUwMCcgOiBnZXRUcmFmZmljTGlnaHRDb2xvcih0ZWNobmljYWxMaWdodClcbiAgICAgICAgICAgICAgICAgICAgfSBzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNzAwIGVhc2UtaW4tb3V0ICR7XG4gICAgICAgICAgICAgICAgICAgICAgdGVjaG5pY2FsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnJyA6ICdhbmltYXRlLXB1bHNlJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIHt0ZWNobmljYWxMaWdodCAhPT0gJ2luYWN0aXZlJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIHctNiBoLTYgcm91bmRlZC1mdWxsICR7Z2V0VHJhZmZpY0xpZ2h0Q29sb3IodGVjaG5pY2FsTGlnaHQpfSBvcGFjaXR5LTIwIGFuaW1hdGUtcGluZ2B9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgIHRlY2huaWNhbExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDogJ3RleHQtd2hpdGUgZ3JvdXAtaG92ZXI6dGV4dC1ibHVlLTMwMCdcbiAgICAgICAgICAgICAgICAgIH0gdHJhbnNpdGlvbi1jb2xvcnNgfT5cbiAgICAgICAgICAgICAgICAgICAg6riw7Iig7KCBIOu2hOyEnVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIGZvbnQtbWVkaXVtIHdoaXRlc3BhY2Utbm93cmFwICR7XG4gICAgICAgICAgICAgICAgICB0ZWNobmljYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6XG4gICAgICAgICAgICAgICAgICB0ZWNobmljYWxMaWdodCA9PT0gJ2dvb2QnID8gJ3RleHQtZ3JlZW4tMzAwJyA6XG4gICAgICAgICAgICAgICAgICB0ZWNobmljYWxMaWdodCA9PT0gJ3dhcm5pbmcnID8gJ3RleHQteWVsbG93LTMwMCcgOlxuICAgICAgICAgICAgICAgICAgJ3RleHQtcmVkLTMwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7dGVjaG5pY2FsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAn64yA6riw7KSRJyA6IGdldFN0YXR1c1RleHQodGVjaG5pY2FsTGlnaHQpfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIExpZ2h0IDI6IEluZHVzdHJ5IFNlbnNpdGl2aXR5ICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctNiBoLTYgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgaW5kdXN0cnlMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICdiZy1ncmF5LTUwMCcgOiBnZXRUcmFmZmljTGlnaHRDb2xvcihpbmR1c3RyeUxpZ2h0KVxuICAgICAgICAgICAgICAgICAgICB9IHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi03MDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgICAgICAgICBpbmR1c3RyeUxpZ2h0ID09PSAnaW5hY3RpdmUnID8gJycgOiAnYW5pbWF0ZS1wdWxzZSdcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7aW5kdXN0cnlMaWdodCAhPT0gJ2luYWN0aXZlJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BhYnNvbHV0ZSBpbnNldC0wIHctNiBoLTYgcm91bmRlZC1mdWxsICR7Z2V0VHJhZmZpY0xpZ2h0Q29sb3IoaW5kdXN0cnlMaWdodCl9IG9wYWNpdHktMjAgYW5pbWF0ZS1waW5nYH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgaW5kdXN0cnlMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6ICd0ZXh0LXdoaXRlIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS0zMDAnXG4gICAgICAgICAgICAgICAgICB9IHRyYW5zaXRpb24tY29sb3JzYH0+XG4gICAgICAgICAgICAgICAgICAgIOyCsOyXhSDrr7zqsJDrj4RcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTIgcHktMSBmb250LW1lZGl1bSB3aGl0ZXNwYWNlLW5vd3JhcCAke1xuICAgICAgICAgICAgICAgICAgaW5kdXN0cnlMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6XG4gICAgICAgICAgICAgICAgICBpbmR1c3RyeUxpZ2h0ID09PSAnZ29vZCcgPyAndGV4dC1ncmVlbi0zMDAnIDpcbiAgICAgICAgICAgICAgICAgIGluZHVzdHJ5TGlnaHQgPT09ICd3YXJuaW5nJyA/ICd0ZXh0LXllbGxvdy0zMDAnIDpcbiAgICAgICAgICAgICAgICAgICd0ZXh0LXJlZC0zMDAnXG4gICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAge2luZHVzdHJ5TGlnaHQgPT09ICdpbmFjdGl2ZScgPyAn64yA6riw7KSRJyA6IGdldFN0YXR1c1RleHQoaW5kdXN0cnlMaWdodCl9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTGlnaHQgMzogTWFya2V0IFNlbnNpdGl2aXR5IChDQVBNKSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B3LTYgaC02IHJvdW5kZWQtZnVsbCAke1xuICAgICAgICAgICAgICAgICAgICAgIG1hcmtldExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ2JnLWdyYXktNTAwJyA6IGdldFRyYWZmaWNMaWdodENvbG9yKG1hcmtldExpZ2h0KVxuICAgICAgICAgICAgICAgICAgICB9IHNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi03MDAgZWFzZS1pbi1vdXQgJHtcbiAgICAgICAgICAgICAgICAgICAgICBtYXJrZXRMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICcnIDogJ2FuaW1hdGUtcHVsc2UnXG4gICAgICAgICAgICAgICAgICAgIH1gfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAge21hcmtldExpZ2h0ICE9PSAnaW5hY3RpdmUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtnZXRUcmFmZmljTGlnaHRDb2xvcihtYXJrZXRMaWdodCl9IG9wYWNpdHktMjAgYW5pbWF0ZS1waW5nYH0+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgbWFya2V0TGlnaHQgPT09ICdpbmFjdGl2ZScgPyAndGV4dC1ncmF5LTQwMCcgOiAndGV4dC13aGl0ZSBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtMzAwJ1xuICAgICAgICAgICAgICAgICAgfSB0cmFuc2l0aW9uLWNvbG9yc2B9PlxuICAgICAgICAgICAgICAgICAgICDsi5zsnqUg66+86rCQ64+EXG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBweC0yIHB5LTEgZm9udC1tZWRpdW0gd2hpdGVzcGFjZS1ub3dyYXAgJHtcbiAgICAgICAgICAgICAgICAgIG1hcmtldExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDpcbiAgICAgICAgICAgICAgICAgIG1hcmtldExpZ2h0ID09PSAnZ29vZCcgPyAndGV4dC1ncmVlbi0zMDAnIDpcbiAgICAgICAgICAgICAgICAgIG1hcmtldExpZ2h0ID09PSAnd2FybmluZycgPyAndGV4dC15ZWxsb3ctMzAwJyA6XG4gICAgICAgICAgICAgICAgICAndGV4dC1yZWQtMzAwJ1xuICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgIHttYXJrZXRMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICfrjIDquLDspJEnIDogZ2V0U3RhdHVzVGV4dChtYXJrZXRMaWdodCl9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTGlnaHQgNDogVm9sYXRpbGl0eSBSaXNrICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctNiBoLTYgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgcmlza0xpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ2JnLWdyYXktNTAwJyA6IGdldFRyYWZmaWNMaWdodENvbG9yKHJpc2tMaWdodClcbiAgICAgICAgICAgICAgICAgICAgfSBzaGFkb3ctbGcgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tNzAwIGVhc2UtaW4tb3V0ICR7XG4gICAgICAgICAgICAgICAgICAgICAgcmlza0xpZ2h0ID09PSAnaW5hY3RpdmUnID8gJycgOiAnYW5pbWF0ZS1wdWxzZSdcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7cmlza0xpZ2h0ICE9PSAnaW5hY3RpdmUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGFic29sdXRlIGluc2V0LTAgdy02IGgtNiByb3VuZGVkLWZ1bGwgJHtnZXRUcmFmZmljTGlnaHRDb2xvcihyaXNrTGlnaHQpfSBvcGFjaXR5LTIwIGFuaW1hdGUtcGluZ2B9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgIHJpc2tMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6ICd0ZXh0LXdoaXRlIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS0zMDAnXG4gICAgICAgICAgICAgICAgICB9IHRyYW5zaXRpb24tY29sb3JzYH0+XG4gICAgICAgICAgICAgICAgICAgIOuzgOuPmeyEsSDrpqzsiqTtgaxcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXhzIHB4LTIgcHktMSBmb250LW1lZGl1bSB3aGl0ZXNwYWNlLW5vd3JhcCAke1xuICAgICAgICAgICAgICAgICAgcmlza0xpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ3RleHQtZ3JheS00MDAnIDpcbiAgICAgICAgICAgICAgICAgIHJpc2tMaWdodCA9PT0gJ2dvb2QnID8gJ3RleHQtZ3JlZW4tMzAwJyA6XG4gICAgICAgICAgICAgICAgICByaXNrTGlnaHQgPT09ICd3YXJuaW5nJyA/ICd0ZXh0LXllbGxvdy0zMDAnIDpcbiAgICAgICAgICAgICAgICAgICd0ZXh0LXJlZC0zMDAnXG4gICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAge3Jpc2tMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICfrjIDquLDspJEnIDogZ2V0U3RhdHVzVGV4dChyaXNrTGlnaHQpfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIExpZ2h0IDU6IE5ldXJhbCBOZXR3b3JrIFByZWRpY3Rpb24gKExTVE0pICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gZ3JvdXBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctNiBoLTYgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICAgICAgbmV1cmFsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAnYmctZ3JheS01MDAnIDogZ2V0VHJhZmZpY0xpZ2h0Q29sb3IobmV1cmFsTGlnaHQpXG4gICAgICAgICAgICAgICAgICAgIH0gc2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTcwMCBlYXNlLWluLW91dCAke1xuICAgICAgICAgICAgICAgICAgICAgIG5ldXJhbExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJycgOiAnYW5pbWF0ZS1wdWxzZSdcbiAgICAgICAgICAgICAgICAgICAgfWB9PjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7bmV1cmFsTGlnaHQgIT09ICdpbmFjdGl2ZScgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgaW5zZXQtMCB3LTYgaC02IHJvdW5kZWQtZnVsbCAke2dldFRyYWZmaWNMaWdodENvbG9yKG5ldXJhbExpZ2h0KX0gb3BhY2l0eS0yMCBhbmltYXRlLXBpbmdgfT48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgey8qIExTVE0gTG9hZGluZyBTcGlubmVyICovfVxuICAgICAgICAgICAgICAgICAgICB7bHN0bUxvYWRpbmcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB3LTYgaC02IHJvdW5kZWQtZnVsbCBib3JkZXItMiBib3JkZXItYmx1ZS01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnQgYW5pbWF0ZS1zcGluXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgbmV1cmFsTGlnaHQgPT09ICdpbmFjdGl2ZScgPyAndGV4dC1ncmF5LTQwMCcgOiAndGV4dC13aGl0ZSBncm91cC1ob3Zlcjp0ZXh0LWJsdWUtMzAwJ1xuICAgICAgICAgICAgICAgICAgfSB0cmFuc2l0aW9uLWNvbG9yc2B9PlxuICAgICAgICAgICAgICAgICAgICB7bHN0bUxvYWRpbmcgPyAn65Sl65+s64udIOq4sOuwmCDsmIjsuKEoTFNUTSknIDogJ+yLoOqyveunnSDsmIjsuKEoTFNUTSknfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgcHgtMiBweS0xIGZvbnQtbWVkaXVtIHdoaXRlc3BhY2Utbm93cmFwICR7XG4gICAgICAgICAgICAgICAgICBuZXVyYWxMaWdodCA9PT0gJ2luYWN0aXZlJyA/ICd0ZXh0LWdyYXktNDAwJyA6XG4gICAgICAgICAgICAgICAgICBuZXVyYWxMaWdodCA9PT0gJ2dvb2QnID8gJ3RleHQtZ3JlZW4tMzAwJyA6XG4gICAgICAgICAgICAgICAgICBuZXVyYWxMaWdodCA9PT0gJ3dhcm5pbmcnID8gJ3RleHQteWVsbG93LTMwMCcgOlxuICAgICAgICAgICAgICAgICAgJ3RleHQtcmVkLTMwMCdcbiAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICB7bHN0bUxvYWRpbmcgPyAn67aE7ISd7KSRLi4uJyA6IG5ldXJhbExpZ2h0ID09PSAnaW5hY3RpdmUnID8gJ+uMgOq4sOykkScgOiBnZXRTdGF0dXNUZXh0KG5ldXJhbExpZ2h0KX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG5cblxuXG5cbiAgICAgICAgey8qIFBoYXNlIDIgTG9hZGluZyBTdGF0ZSAqL31cbiAgICAgICAge3BoYXNlMkxvYWRpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtMTAwIHJvdW5kZWQtbGcgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC02IHctNiBib3JkZXItMiBib3JkZXItYmx1ZS01MDAgYm9yZGVyLXQtdHJhbnNwYXJlbnRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTYwMFwiPlxuICAgICAgICAgICAgICAgIOuUpeufrOuLnSDquLDrsJgg7JiI7LihKExTVE0pIOu2hOyEnSDspJEuLi5cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qIEtvcmVhbiBUaW1lb3V0IE1lc3NhZ2UgKi99XG4gICAgICAgIHtzaG93VGltZW91dE1lc3NhZ2UgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTUwMC8xMCBib3JkZXIgYm9yZGVyLXllbGxvdy01MDAvMjAgcm91bmRlZC1sZyBwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXllbGxvdy02MDAgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAg7J20IOyekeyXheydgCDsi5zqsITsnbQg6rG466a964uI64ukLi4uICjstZzrjIAgNjDstIgpXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogRXJyb3IgRGlzcGxheSAqL31cbiAgICAgICAge3ByZWRpY3Rpb25FcnJvciAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAwLzEwIGJvcmRlciBib3JkZXItcmVkLTUwMC8yMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgdGV4dC14cyB0ZXh0LXJlZC02MDBcIj5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDl2Mm0wIDRoLjAxbS02LjkzOCA0aDEzLjg1NmMxLjU0IDAgMi41MDItMS42NjcgMS43MzItMi41TDEzLjczMiA0Yy0uNzctLjgzMy0xLjk2NC0uODMzLTIuNzMyIDBMMy43MzIgMTYuNWMtLjc3LjgzMy4xOTIgMi41IDEuNzMyIDIuNXpcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgPHNwYW4+7Jik66WYOiB7cHJlZGljdGlvbkVycm9yfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBSZXRyeSBCdXR0b24gKi99XG4gICAgICAgIHtwcmVkaWN0aW9uRXJyb3IgJiYgIXBoYXNlMUxvYWRpbmcgJiYgIXBoYXNlMkxvYWRpbmcgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17ZmV0Y2hTdGFnZWRQcmVkaWN0aW9ufVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRleHQtc20gaG92ZXI6YmctYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDri6Tsi5wg7Iuc64+EXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuXG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8gUHJlLXRpY2tlciBtb2RlOiBNYXJrZXQgaW5kaWNhdG9ycyBkaXNwbGF5XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIHsvKiDtl6TrjZQgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTkwMFwiPuyLnOyepSDtmITtmak8L2gzPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHctMiBoLTIgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2UgJHtsb2FkaW5nID8gJ2JnLXllbGxvdy00MDAnIDogJ2JnLWdyZWVuLTQwMCd9YH0+PC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOyngO2RnCDrqqnroZ0gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICB7bG9hZGluZyAmJiBpbmRpY2F0b3JzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAvLyDroZzrlKkg7Iqk7LyI66CI7YakXG4gICAgICAgICAgQXJyYXkuZnJvbSh7IGxlbmd0aDogNSB9KS5tYXAoKF8sIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInAtMyBiZy1zbGF0ZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc2xhdGUtMTAwIGFuaW1hdGUtcHVsc2VcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLXNsYXRlLTIwMCByb3VuZGVkIHctMTZcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctNCBoLTQgYmctc2xhdGUtMjAwIHJvdW5kZWRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTYgYmctc2xhdGUtMjAwIHJvdW5kZWQgdy0yMFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLXNsYXRlLTIwMCByb3VuZGVkIHctMTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKVxuICAgICAgICApIDogKFxuICAgICAgICAgIGluZGljYXRvcnMubWFwKChpbmRpY2F0b3IsIGluZGV4KSA9PiAoXG4gICAgICAgICAgPGRpdlxuICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMyBiZy1zbGF0ZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItc2xhdGUtMTAwIGhvdmVyOmJnLXNsYXRlLTEwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXNsYXRlLTcwMFwiPntpbmRpY2F0b3IubGFiZWx9PC9zcGFuPlxuICAgICAgICAgICAgICB7Z2V0VHJlbmRJY29uKGluZGljYXRvci50cmVuZCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXNsYXRlLTkwMFwiPntpbmRpY2F0b3IudmFsdWV9PC9zcGFuPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtbWVkaXVtICR7Z2V0Q2hhbmdlQ29sb3IoaW5kaWNhdG9yLnRyZW5kKX1gfT5cbiAgICAgICAgICAgICAgICB7aW5kaWNhdG9yLmNoYW5nZX1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSlcbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog7ZG47YSwICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0zIGJvcmRlci10IGJvcmRlci1zbGF0ZS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTIgdGV4dC14cyB0ZXh0LXNsYXRlLTUwMFwiPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy0zIGgtM1wiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDh2NGwzIDNtNi0zYTkgOSAwIDExLTE4IDAgOSA5IDAgMDExOCAwelwiIC8+XG4gICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICB7bG9hZGluZyA/ICfsl4XrjbDsnbTtirgg7KSRLi4uJyA6XG4gICAgICAgICAgICAgbGFzdFVwZGF0ZSA/IGDrp4jsp4Drp4kg7JeF642w7J207Yq4OiAke2xhc3RVcGRhdGV9YCA6XG4gICAgICAgICAgICAgJzIw7LSI66eI64ukIOyXheuNsOydtO2KuCd9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgU3BlZWRUcmFmZmljO1xuXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJlZiIsImdldENvbXBhbnlOYW1lIiwiU3BlZWRUcmFmZmljIiwic3ltYm9sIiwib25QaGFzZU1lc3NhZ2UiLCJvbkFuYWx5c2lzQ29tcGxldGUiLCJpbmRpY2F0b3JzIiwic2V0SW5kaWNhdG9ycyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwibGFzdFVwZGF0ZSIsInNldExhc3RVcGRhdGUiLCJ0ZWNobmljYWxMaWdodCIsInNldFRlY2huaWNhbExpZ2h0IiwiaW5kdXN0cnlMaWdodCIsInNldEluZHVzdHJ5TGlnaHQiLCJtYXJrZXRMaWdodCIsInNldE1hcmtldExpZ2h0Iiwicmlza0xpZ2h0Iiwic2V0Umlza0xpZ2h0IiwibmV1cmFsTGlnaHQiLCJzZXROZXVyYWxMaWdodCIsInBoYXNlMUxvYWRpbmciLCJzZXRQaGFzZTFMb2FkaW5nIiwicGhhc2UyTG9hZGluZyIsInNldFBoYXNlMkxvYWRpbmciLCJsc3RtTG9hZGluZyIsInNldExzdG1Mb2FkaW5nIiwicHJlZGljdGlvbkVycm9yIiwic2V0UHJlZGljdGlvbkVycm9yIiwic2hvd1RpbWVvdXRNZXNzYWdlIiwic2V0U2hvd1RpbWVvdXRNZXNzYWdlIiwibGFzdFJlcXVlc3RUaW1lIiwic2V0TGFzdFJlcXVlc3RUaW1lIiwiYWxsUmVzdWx0cyIsInNldEFsbFJlc3VsdHMiLCJpbkZsaWdodCIsImZldGNoTWFya2V0RGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJmb3JtYXR0ZWREYXRhIiwibWFwIiwiaXRlbSIsImxhYmVsIiwidmFsdWUiLCJwcmljZSIsInRvTG9jYWxlU3RyaW5nIiwibWluaW11bUZyYWN0aW9uRGlnaXRzIiwibWF4aW11bUZyYWN0aW9uRGlnaXRzIiwiY2hhbmdlIiwiY2hhbmdlUGVyY2VudCIsInRvRml4ZWQiLCJ0cmVuZCIsImNvbG9yIiwiRGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImVycm9yIiwiY29uc29sZSIsInJlc3VsdENvbG9yVG9TdGF0dXMiLCJ0b0xvd2VyQ2FzZSIsImdldENvbG9yRnJvbVJlc3VsdCIsIm9iaiIsInVuZGVmaW5lZCIsInJlc3VsdF9jb2xvciIsInRyYWZmaWNfbGlnaHQiLCJmZXRjaFN0YWdlZFByZWRpY3Rpb24iLCJjdXJyZW50Iiwibm93IiwibG9nIiwicGhhc2UyUmVzdWx0IiwiY29tcGFueU5hbWUiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJwaGFzZTFSZXNwb25zZSIsIm1ldGhvZCIsImhlYWRlcnMiLCJzaWduYWwiLCJBYm9ydFNpZ25hbCIsInRpbWVvdXQiLCJvayIsIkVycm9yIiwic3RhdHVzIiwic3RhdHVzVGV4dCIsInBoYXNlMVJlc3VsdCIsInRyYWZmaWNfbGlnaHRzIiwidGVjaG5pY2FsIiwiaW5kdXN0cnkiLCJtYXJrZXQiLCJyaXNrIiwicHJldiIsIm1maSIsImJvbGxpbmdlciIsInJzaSIsImNhcG0iLCJnYXJjaCIsInRpbWVvdXRUaW1lciIsInBoYXNlMlJlc3BvbnNlIiwiY2xlYXJUaW1lb3V0IiwibmV1cmFsIiwiZmluYWxSZXN1bHRzIiwidGltZXN0YW1wIiwidG9JU09TdHJpbmciLCJhbmFseXNpc0RhdGUiLCJzcGxpdCIsImxzdG0iLCJzYXZlUmVzcG9uc2UiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInNhdmVSZXN1bHQiLCJmaWxlcGF0aCIsInRleHQiLCJuYW1lIiwibWVzc2FnZSIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwiZ2V0VHJlbmRJY29uIiwic3ZnIiwiY2xhc3NOYW1lIiwiZmlsbCIsInN0cm9rZSIsInZpZXdCb3giLCJwYXRoIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJkIiwiZ2V0Q2hhbmdlQ29sb3IiLCJnZXRUcmFmZmljTGlnaHRDb2xvciIsImdldFN0YXR1c1RleHQiLCJkaXYiLCJoMiIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwiaDMiLCJsZW5ndGgiLCJBcnJheSIsImZyb20iLCJfIiwiaW5kZXgiLCJpbmRpY2F0b3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SpeedTraffic.tsx\n"));

/***/ })

});