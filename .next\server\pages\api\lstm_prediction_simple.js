"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lstm_prediction_simple";
exports.ids = ["pages/api/lstm_prediction_simple"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\lstm_prediction_simple.ts */ \"(api)/./src/pages/api/lstm_prediction_simple.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lstm_prediction_simple\",\n        pathname: \"/api/lstm_prediction_simple\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_lstm_prediction_simple_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/lstm_prediction_simple.ts":
/*!*************************************************!*\
  !*** ./src/pages/api/lstm_prediction_simple.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Per-symbol mutex using in-memory Map with timestamps\nconst processing = new Map();\n// Circuit breaker pattern - track failures per symbol\nconst failureCount = new Map();\nconst lastFailureTime = new Map();\nconst FAILURE_THRESHOLD = 3;\nconst CIRCUIT_BREAKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes\n// Cleanup stale processing entries (older than 5 minutes)\nconst cleanupStaleProcessing = ()=>{\n    const now = Date.now();\n    const fiveMinutes = 5 * 60 * 1000;\n    for (const [symbol, info] of processing.entries()){\n        if (info.active && now - info.startTime > fiveMinutes) {\n            console.log(`[LSTM_SIMPLE_API] Cleaning up stale processing entry for ${symbol}`);\n            processing.delete(symbol);\n        }\n    }\n};\n// Check if circuit breaker is open for a symbol\nconst isCircuitBreakerOpen = (symbol)=>{\n    const failures = failureCount.get(symbol) || 0;\n    const lastFailure = lastFailureTime.get(symbol) || 0;\n    const now = Date.now();\n    if (failures >= FAILURE_THRESHOLD) {\n        if (now - lastFailure < CIRCUIT_BREAKER_TIMEOUT) {\n            return true; // Circuit breaker is open\n        } else {\n            // Reset circuit breaker after timeout\n            failureCount.delete(symbol);\n            lastFailureTime.delete(symbol);\n            return false;\n        }\n    }\n    return false;\n};\n// Record a failure for circuit breaker\nconst recordFailure = (symbol)=>{\n    const failures = (failureCount.get(symbol) || 0) + 1;\n    failureCount.set(symbol, failures);\n    lastFailureTime.set(symbol, Date.now());\n    console.log(`[LSTM_SIMPLE_API] Recorded failure ${failures} for ${symbol}`);\n};\n// Run cleanup every minute\nsetInterval(cleanupStaleProcessing, 60 * 1000);\n// Korean summary mapping\nconst getKoreanSummary = (predictions)=>{\n    const correctCount = predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    if (correctCount === 2) {\n        return \"모두 예측 성공 결과 : green\";\n    } else if (correctCount === 1) {\n        return \"2일 예측 중 1일 예측 실패 결과 : yellow\";\n    } else {\n        return \"모두 예측 실패 결과 : red\";\n    }\n};\n// Traffic light color determination\nconst getTrafficLightColor = (result, processType)=>{\n    if (processType === 'LSTM') {\n        // Check for deactivation status first (accuracy 0 or 1)\n        if (result.is_deactivated === true || result.traffic_light === 'inactive') {\n            return 'inactive';\n        }\n        // Use the traffic_light field from LSTM service (probability-based)\n        if (result.traffic_light) {\n            return result.traffic_light.toLowerCase();\n        }\n        // Legacy fallback: accuracy-based calculation with deactivation logic\n        if (result.predictions && Array.isArray(result.predictions)) {\n            const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n            // Deactivate if accuracy is 0 or 1\n            if (correctCount <= 1) return 'inactive';\n            if (correctCount === 2) return 'green';\n            return 'yellow';\n        }\n        // Check accuracy field directly\n        if (typeof result.accuracy === 'number' && result.accuracy <= 1) {\n            return 'inactive';\n        }\n        // Final fallback\n        return result.result_color || result.color || 'yellow';\n    } else if (processType === 'MFI') {\n        return result.traffic_light || result.color || 'yellow';\n    }\n    return 'yellow';\n};\n// Normalize LSTM accuracy to decimal format\nconst normalizeLSTMAccuracy = (rawAccuracy)=>{\n    // Convert raw accuracy values to decimal format:\n    // 3 correct predictions → 0.600 (3/5)\n    // 2 correct predictions → 0.400 (2/5)\n    // 5+ correct predictions → 1.00 (capped at 1.0)\n    if (rawAccuracy >= 5) {\n        return 1.0;\n    }\n    return Math.round(rawAccuracy / 5 * 1000) / 1000; // Round to 3 decimal places\n};\n// Calculate LSTM accuracy percentage\nconst calculateLSTMAccuracy = (result)=>{\n    // For lstm_finetuning.py output, use the accuracy field directly\n    if (typeof result.accuracy === 'number') {\n        // If accuracy is already normalized (decimal), return as is\n        if (result.accuracy <= 1.0) {\n            return result.accuracy;\n        }\n        // If accuracy is raw count, normalize it\n        return normalizeLSTMAccuracy(result.accuracy);\n    }\n    // Legacy fallback for lstm_service.py output\n    if (!Array.isArray(result.predictions) || result.predictions.length === 0) return 0;\n    const correctCount = result.predictions.filter((p)=>p.predicted_label === p.actual_label).length;\n    return normalizeLSTMAccuracy(correctCount);\n};\n// Get LSTM pred_prob_up value\nconst getLSTMPredProbUp = (result)=>{\n    if (result.predictions && Array.isArray(result.predictions) && result.predictions.length > 0) {\n        return result.predictions[0].pred_prob_up || 0;\n    }\n    return result.pred_prob_up || 0;\n};\n// Get MFI numerical value\nconst getMFIValue = (result)=>{\n    return result.mfi_14 || result.mfi || 0;\n};\n// Get service-specific values for logging and fine-tuning\nconst getBollingerValue = (result)=>{\n    return result.percent_b || result.bollinger_position || result.position || 0;\n};\nconst getRSIValue = (result)=>{\n    return result.rsi_14 || result.rsi || 0;\n};\nconst getIndustryValue = (result)=>{\n    return result.industry || result.industry_sentiment || result.sentiment || 'neutral';\n};\nconst getGARCHValue = (result)=>{\n    return result.var95_pct || result.var_pct || result.volatility_forecast || result.volatility || 0;\n};\n// Get traffic light color from service results\nconst getServiceTrafficLight = (result)=>{\n    return result.traffic_light || result.color || 'yellow';\n};\n// Majority vote logic for technical analysis (MFI, Bollinger, RSI)\nconst getTechnicalMajorityVote = (mfiColor, bollingerColor, rsiColor)=>{\n    const colors = [\n        mfiColor,\n        bollingerColor,\n        rsiColor\n    ];\n    const colorCounts = {\n        green: colors.filter((c)=>c === 'green').length,\n        yellow: colors.filter((c)=>c === 'yellow').length,\n        red: colors.filter((c)=>c === 'red').length\n    };\n    // Return the color with the highest count\n    if (colorCounts.green >= 2) return 'green';\n    if (colorCounts.red >= 2) return 'red';\n    return 'yellow'; // Default to yellow if no majority or all different\n};\n// Execute a process and return its result with enhanced logging\nconst executeProcess = (scriptPath, ticker, processName)=>{\n    return new Promise((resolve, reject)=>{\n        const servicesDir = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services');\n        const startTime = Date.now();\n        console.log(`[LSTM_SIMPLE_API] Starting ${processName} process for ${ticker}`);\n        // Special handling for LSTM finetuning script which requires date parameter\n        const args = processName === 'LSTM' ? [\n            scriptPath,\n            ticker,\n            '2025-06-05'\n        ] : [\n            scriptPath,\n            ticker\n        ];\n        const childProcess = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', args, {\n            cwd: servicesDir,\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ],\n            env: {\n                ...process.env,\n                PYTHONUNBUFFERED: '1'\n            }\n        });\n        let stdout = '';\n        let stderr = '';\n        childProcess.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        childProcess.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        childProcess.on('close', (code)=>{\n            const executionTime = Date.now() - startTime;\n            console.log(`[LSTM_SIMPLE_API] ${processName} process closed for ${ticker} with code ${code} (${executionTime}ms)`);\n            if (code === 0) {\n                try {\n                    const lines = stdout.trim().split('\\n').filter((line)=>line.trim());\n                    let jsonOutput;\n                    if (processName === 'LSTM') {\n                        // Parse LSTM finetuning output format: [LSTM accuracy: 3, Prediction probability up: 0.623, Traffic light: GREEN]\n                        let lstmResultLine = null;\n                        for(let i = lines.length - 1; i >= 0; i--){\n                            const line = lines[i].trim();\n                            if (line.startsWith('[LSTM accuracy:')) {\n                                lstmResultLine = line;\n                                break;\n                            }\n                        }\n                        if (!lstmResultLine) {\n                            throw new Error(`No LSTM result found in stdout`);\n                        }\n                        // Parse the LSTM result line\n                        const accuracyMatch = lstmResultLine.match(/accuracy:\\s*(\\d+)/);\n                        const probMatch = lstmResultLine.match(/probability up:\\s*([\\d.]+)/);\n                        const trafficMatch = lstmResultLine.match(/Traffic light:\\s*(\\w+)/);\n                        const rawAccuracy = accuracyMatch ? parseInt(accuracyMatch[1]) : 0;\n                        const probUp = probMatch ? parseFloat(probMatch[1]) : 0.0;\n                        const trafficLight = trafficMatch ? trafficMatch[1].toLowerCase() : 'red';\n                        jsonOutput = {\n                            accuracy: normalizeLSTMAccuracy(rawAccuracy),\n                            pred_prob_up: probUp,\n                            traffic_light: trafficLight,\n                            predictions: [] // Empty array for compatibility\n                        };\n                    } else {\n                        // Parse the JSON output from the last line for other services\n                        let jsonLine = null;\n                        for(let i = lines.length - 1; i >= 0; i--){\n                            const line = lines[i].trim();\n                            if (line.startsWith('{')) {\n                                jsonLine = line;\n                                break;\n                            }\n                        }\n                        if (!jsonLine) {\n                            throw new Error(`No JSON output found in ${processName} stdout`);\n                        }\n                        jsonOutput = JSON.parse(jsonLine);\n                    }\n                    // Enhanced result logging\n                    if (processName === 'LSTM') {\n                        const accuracy = calculateLSTMAccuracy(jsonOutput);\n                        const predProbUp = getLSTMPredProbUp(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'LSTM');\n                        console.log(`[LSTM_RESULT] ${ticker} - Accuracy: ${accuracy}%, pred_prob_up: ${predProbUp}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'MFI') {\n                        const mfiValue = getMFIValue(jsonOutput);\n                        const trafficLight = getTrafficLightColor(jsonOutput, 'MFI');\n                        console.log(`[MFI_RESULT] ${ticker} - MFI Value: ${mfiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'BOLLINGER') {\n                        const bollingerValue = getBollingerValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[BOLLINGER_RESULT] ${ticker} - Percent B: ${bollingerValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'RSI') {\n                        const rsiValue = getRSIValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[RSI_RESULT] ${ticker} - RSI Value: ${rsiValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'INDUSTRY') {\n                        const industryValue = getIndustryValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[INDUSTRY_RESULT] ${ticker} - Industry: ${industryValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'GARCH') {\n                        const garchValue = getGARCHValue(jsonOutput);\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[GARCH_RESULT] ${ticker} - VaR %: ${garchValue}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    } else if (processName === 'CAPM') {\n                        const capmBeta = jsonOutput.beta_market || 0;\n                        const capmR2 = jsonOutput.r2_market || 0;\n                        const trafficLight = getServiceTrafficLight(jsonOutput);\n                        console.log(`[CAPM_RESULT] ${ticker} - Beta: ${capmBeta}, R²: ${capmR2}, Traffic Light: ${trafficLight.toUpperCase()}, Execution Time: ${executionTime}ms, Status: SUCCESS`);\n                    }\n                    resolve(jsonOutput);\n                } catch (parseError) {\n                    console.error(`[${processName}_RESULT] ${ticker} - Status: PARSE_ERROR, Execution Time: ${executionTime}ms, Error: ${parseError}`);\n                    reject(new Error(`Failed to parse ${processName} output: ${parseError}`));\n                }\n            } else {\n                console.error(`[${processName}_RESULT] ${ticker} - Status: PROCESS_FAILED, Execution Time: ${executionTime}ms, Exit Code: ${code}`);\n                console.error(`[${processName}_RESULT] ${ticker} - stderr:`, stderr);\n                reject(new Error(`${processName} process failed with code ${code}`));\n            }\n        });\n        childProcess.on('error', (error)=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: SPAWN_ERROR, Execution Time: ${executionTime}ms, Error: ${error.message}`);\n            reject(new Error(`${processName} process error: ${error.message}`));\n        });\n        // Set timeout for individual process (60 seconds)\n        setTimeout(()=>{\n            const executionTime = Date.now() - startTime;\n            console.error(`[${processName}_RESULT] ${ticker} - Status: TIMEOUT, Execution Time: ${executionTime}ms`);\n            childProcess.kill('SIGTERM');\n            reject(new Error(`${processName} process timed out after 60 seconds`));\n        }, 60000);\n    });\n};\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    const { symbol } = req.query;\n    if (!symbol || typeof symbol !== 'string') {\n        return res.status(400).json({\n            error: 'Symbol is required'\n        });\n    }\n    const ticker = symbol.toUpperCase();\n    // Check circuit breaker\n    if (isCircuitBreakerOpen(ticker)) {\n        return res.status(503).json({\n            error: 'Service temporarily unavailable due to repeated failures',\n            retryAfter: Math.ceil(CIRCUIT_BREAKER_TIMEOUT / 1000)\n        });\n    }\n    // Check if already processing\n    const currentProcessing = processing.get(ticker);\n    if (currentProcessing?.active) {\n        return res.status(429).json({\n            error: 'Already processing this symbol',\n            retryAfter: 15\n        });\n    }\n    // Set processing flag\n    processing.set(ticker, {\n        active: true,\n        startTime: Date.now()\n    });\n    try {\n        console.log(`[LSTM_SIMPLE_API] Starting prediction for ${ticker}`);\n        // Check if this is a staged execution request\n        const { stage } = req.query;\n        // Paths to service scripts\n        const lstmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'lstm_finetuning.py');\n        const mfiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'mfi_service.py');\n        const bollingerScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'bollinger_service.py');\n        const rsiScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'rsi_service.py');\n        const industryScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'industry_regression_service.py');\n        const garchScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'garch_service.py');\n        const capmScriptPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'capm_service.py');\n        // Staged execution: Phase 1 (Fast services) vs Phase 2 (LSTM)\n        if (stage === 'phase1') {\n            console.log(`[LSTM_SIMPLE_API] Starting Phase 1 (fast services) for ${ticker}`);\n            // Execute Phase 1: Technical, Industry, Market, Volatility services in parallel\n            const [mfiResult, bollingerResult, rsiResult, industryResult, capmResult, garchResult] = await Promise.allSettled([\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath) ? executeProcess(mfiScriptPath, ticker, 'MFI') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(capmScriptPath) ? executeProcess(capmScriptPath, ticker, 'CAPM') : Promise.resolve(null),\n                fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n            ]);\n            // Extract results with error handling\n            const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n            const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n            const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n            const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n            const finalCAPMResult = capmResult.status === 'fulfilled' ? capmResult.value : null;\n            const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n            // Log any service failures\n            if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n            if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n            if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n            if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n            if (capmResult.status === 'rejected') console.error(`[CAPM_ERROR] ${ticker}:`, capmResult.reason);\n            if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n            // Calculate technical majority vote for traffic light\n            const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n            const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n            const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n            const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n            console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n            // Phase 1 result structure\n            const phase1Result = {\n                phase: 1,\n                mfi: finalMFIResult,\n                bollinger: finalBollingerResult,\n                rsi: finalRSIResult,\n                industry: finalIndustryResult,\n                capm: finalCAPMResult,\n                garch: finalGARCHResult,\n                traffic_lights: {\n                    technical: technicalColor,\n                    industry: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                    market: finalCAPMResult ? getServiceTrafficLight(finalCAPMResult) : 'inactive',\n                    risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive' // Light 4: Volatility Risk\n                }\n            };\n            console.log(`[LSTM_SIMPLE_API] Phase 1 completed successfully for ${ticker}`);\n            return res.status(200).json(phase1Result);\n        } else if (stage === 'phase2') {\n            console.log(`[LSTM_SIMPLE_API] Starting Phase 2 (LSTM) for ${ticker}`);\n            // Validate LSTM script path exists\n            if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n                throw new Error('LSTM service script not found');\n            }\n            // Execute Phase 2: LSTM service only\n            const lstmResult = await executeProcess(lstmScriptPath, ticker, 'LSTM');\n            // Add Korean summary to LSTM result\n            if (lstmResult?.predictions) {\n                lstmResult.summary_ko = getKoreanSummary(lstmResult.predictions);\n            }\n            // Phase 2 result structure\n            const phase2Result = {\n                phase: 2,\n                lstm: lstmResult,\n                traffic_lights: {\n                    neural: lstmResult ? getTrafficLightColor(lstmResult, 'LSTM') : 'red' // Light 5: Neural Network Prediction\n                }\n            };\n            console.log(`[LSTM_SIMPLE_API] Phase 2 completed successfully for ${ticker}`);\n            return res.status(200).json(phase2Result);\n        }\n        // Legacy mode: Execute all processes in parallel (backward compatibility)\n        console.log(`[LSTM_SIMPLE_API] Starting legacy mode (all services) for ${ticker}`);\n        // Validate required script paths exist\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(lstmScriptPath)) {\n            throw new Error('LSTM service script not found');\n        }\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(mfiScriptPath)) {\n            throw new Error('MFI service script not found');\n        }\n        // Execute all processes in parallel with graceful error handling\n        const [lstmResult, mfiResult, bollingerResult, rsiResult, industryResult, capmResult, garchResult] = await Promise.allSettled([\n            executeProcess(lstmScriptPath, ticker, 'LSTM'),\n            executeProcess(mfiScriptPath, ticker, 'MFI'),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(bollingerScriptPath) ? executeProcess(bollingerScriptPath, ticker, 'BOLLINGER') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(rsiScriptPath) ? executeProcess(rsiScriptPath, ticker, 'RSI') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(industryScriptPath) ? executeProcess(industryScriptPath, ticker, 'INDUSTRY') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(capmScriptPath) ? executeProcess(capmScriptPath, ticker, 'CAPM') : Promise.resolve(null),\n            fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(garchScriptPath) ? executeProcess(garchScriptPath, ticker, 'GARCH') : Promise.resolve(null)\n        ]);\n        // Extract results with error handling\n        const finalLSTMResult = lstmResult.status === 'fulfilled' ? lstmResult.value : null;\n        const finalMFIResult = mfiResult.status === 'fulfilled' ? mfiResult.value : null;\n        const finalBollingerResult = bollingerResult.status === 'fulfilled' ? bollingerResult.value : null;\n        const finalRSIResult = rsiResult.status === 'fulfilled' ? rsiResult.value : null;\n        const finalIndustryResult = industryResult.status === 'fulfilled' ? industryResult.value : null;\n        const finalCAPMResult = capmResult.status === 'fulfilled' ? capmResult.value : null;\n        const finalGARCHResult = garchResult.status === 'fulfilled' ? garchResult.value : null;\n        // Log any service failures\n        if (lstmResult.status === 'rejected') console.error(`[LSTM_ERROR] ${ticker}:`, lstmResult.reason);\n        if (mfiResult.status === 'rejected') console.error(`[MFI_ERROR] ${ticker}:`, mfiResult.reason);\n        if (bollingerResult.status === 'rejected') console.error(`[BOLLINGER_ERROR] ${ticker}:`, bollingerResult.reason);\n        if (rsiResult.status === 'rejected') console.error(`[RSI_ERROR] ${ticker}:`, rsiResult.reason);\n        if (industryResult.status === 'rejected') console.error(`[INDUSTRY_ERROR] ${ticker}:`, industryResult.reason);\n        if (capmResult.status === 'rejected') console.error(`[CAPM_ERROR] ${ticker}:`, capmResult.reason);\n        if (garchResult.status === 'rejected') console.error(`[GARCH_ERROR] ${ticker}:`, garchResult.reason);\n        // Add Korean summary to LSTM result\n        if (finalLSTMResult?.predictions) {\n            finalLSTMResult.summary_ko = getKoreanSummary(finalLSTMResult.predictions);\n        }\n        // Calculate technical majority vote for traffic light\n        const mfiColor = finalMFIResult ? getTrafficLightColor(finalMFIResult, 'MFI') : 'red';\n        const bollingerColor = finalBollingerResult ? getServiceTrafficLight(finalBollingerResult) : 'red';\n        const rsiColor = finalRSIResult ? getServiceTrafficLight(finalRSIResult) : 'red';\n        const technicalColor = getTechnicalMajorityVote(mfiColor, bollingerColor, rsiColor);\n        console.log(`[TECHNICAL_VOTE] ${ticker} - MFI: ${mfiColor}, Bollinger: ${bollingerColor}, RSI: ${rsiColor} → Technical: ${technicalColor.toUpperCase()}`);\n        // Note: Fine-tuning data is now saved by SpeedTraffic component after complete analysis\n        // Merge results with enhanced structure (legacy mode)\n        const mergedResult = {\n            lstm: finalLSTMResult,\n            mfi: finalMFIResult,\n            bollinger: finalBollingerResult,\n            rsi: finalRSIResult,\n            industry: finalIndustryResult,\n            capm: finalCAPMResult,\n            garch: finalGARCHResult,\n            traffic_lights: {\n                technical: technicalColor,\n                industry: finalIndustryResult ? getServiceTrafficLight(finalIndustryResult) : 'inactive',\n                market: finalCAPMResult ? getServiceTrafficLight(finalCAPMResult) : 'inactive',\n                risk: finalGARCHResult ? getServiceTrafficLight(finalGARCHResult) : 'inactive',\n                neural: finalLSTMResult ? getTrafficLightColor(finalLSTMResult, 'LSTM') : 'red' // Light 5: Neural Network Prediction\n            }\n        };\n        console.log(`[LSTM_SIMPLE_API] Prediction completed successfully for ${ticker} with ${Object.keys(mergedResult.traffic_lights).length} traffic lights`);\n        // Return the merged result\n        res.status(200).json(mergedResult);\n    } catch (error) {\n        console.error(`[LSTM_SIMPLE_API] Prediction error for ${ticker}:`, error);\n        // Record failure for circuit breaker\n        recordFailure(ticker);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Prediction failed',\n            message: errorMessage,\n            timestamp: new Date().toISOString()\n        });\n    } finally{\n        // Clean up processing flag\n        processing.delete(ticker);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/lstm_prediction_simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_prediction_simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_prediction_simple.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();