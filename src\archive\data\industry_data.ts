// final_industry_data.ts
// 확장된 산업군 분류 데이터

import { QUICK } from '../data/quick';

// 산업군 데이터 구조 정의
export interface IndustryGroup {
  sector: string;
  industry: string;
  subIndustry: string;
  companies: string[];
}

// 산업군 맵 정의
export const INDUSTRY_MAP: Record<string, IndustryGroup> = {
  // 반도체 산업군
  "SEMICONDUCTORS": {
    sector: "Information Technology",
    industry: "Semiconductors & Semiconductor Equipment",
    subIndustry: "Semiconductors",
    companies: ["NVDA", "AMD", "INTC", "QCOM", "AVGO", "MU", "TXN", "AMAT", "LRCX", "KLAC"]
  },
  // 소프트웨어 산업군
  "SOFTWARE": {
    sector: "Information Technology",
    industry: "Software & Services",
    subIndustry: "Application Software",
    companies: ["MSFT", "ADBE", "CRM", "INTU", "ADSK", "WDAY", "NOW", "PLTR", "ZM", "TEAM"]
  },
  // 인공지능
  "ARTIFICIAL_INTELLIGENCE": {
    sector: "Information Technology",
    industry: "Software & Services",
    subIndustry: "Artificial Intelligence",
    companies: ["NVDA", "GOOGL", "MSFT", "META", "AMZN", "IBM", "PLTR", "AI", "UPST", "SPLK"]
  },
  // 자동차
  "AUTOMOTIVE": {
    sector: "Consumer Discretionary",
    industry: "Automobiles & Components",
    subIndustry: "Automobile Manufacturers",
    companies: ["TSLA", "GM", "F", "LCID", "RIVN", "NIO", "XPEV", "LI", "FSR", "GOEV"]
  },
  // 전자상거래
  "ECOMMERCE": {
    sector: "Consumer Discretionary",
    industry: "Internet & Direct Marketing Retail",
    subIndustry: "E-Commerce",
    companies: ["AMZN", "EBAY", "ETSY", "W", "CHWY", "OSTK", "WISH", "BABA", "JD", "PDD"]
  },
  // 금융 서비스
  "FINANCIAL_SERVICES": {
    sector: "Financials",
    industry: "Diversified Financial Services",
    subIndustry: "Financial Services",
    companies: ["JPM", "BAC", "WFC", "C", "GS", "MS", "AXP", "V", "MA", "PYPL"]
  },
  // 헬스케어
  "HEALTHCARE": {
    sector: "Health Care",
    industry: "Health Care Equipment & Services",
    subIndustry: "Health Care",
    companies: ["JNJ", "UNH", "PFE", "MRK", "ABBV", "LLY", "TMO", "ABT", "DHR", "BMY"]
  },
  // 통신
  "TELECOMMUNICATIONS": {
    sector: "Communication Services",
    industry: "Telecommunication Services",
    subIndustry: "Telecommunications",
    companies: ["T", "VZ", "TMUS", "CMCSA", "CHTR", "LUMN", "DISH", "CCOI", "BAND", "OOMA"]
  },
  // 에너지
  "ENERGY": {
    sector: "Energy",
    industry: "Energy",
    subIndustry: "Energy",
    companies: ["XOM", "CVX", "COP", "EOG", "SLB", "PXD", "OXY", "MPC", "PSX", "VLO"]
  },
  // 유틸리티
  "UTILITIES": {
    sector: "Utilities",
    industry: "Utilities",
    subIndustry: "Utilities",
    companies: ["NEE", "DUK", "SO", "D", "AEP", "XEL", "EXC", "PCG", "ED", "PEG"]
  },
  // ETF
  "ETF": {
    sector: "Exchange Traded Fund",
    industry: "Exchange Traded Fund",
    subIndustry: "Exchange Traded Fund",
    companies: ["SPY", "QQQ", "IWM", "DIA", "VTI", "VOO", "VEA", "VWO", "BND", "AGG"]
  },
  // 항공
  "AIRLINES": {
    sector: "Industrials",
    industry: "Transportation",
    subIndustry: "Airlines",
    companies: ["AAL", "DAL", "UAL", "LUV", "JBLU", "ALK", "SAVE", "HA", "SKYW", "MESA"]
  },
  // 소매
  "RETAIL": {
    sector: "Consumer Discretionary",
    industry: "Retailing",
    subIndustry: "Retail",
    companies: ["WMT", "TGT", "HD", "LOW", "COST", "AMZN", "BBY", "DG", "DLTR", "KR"]
  },
  // 미디어 & 엔터테인먼트
  "MEDIA_ENTERTAINMENT": {
    sector: "Communication Services",
    industry: "Media & Entertainment",
    subIndustry: "Media & Entertainment",
    companies: ["NFLX", "DIS", "CMCSA", "PARA", "WBD", "META", "GOOGL", "SPOT", "ROKU", "SIRI"]
  },
  // 부동산
  "REAL_ESTATE": {
    sector: "Real Estate",
    industry: "Real Estate",
    subIndustry: "Real Estate",
    companies: ["AMT", "PLD", "CCI", "EQIX", "PSA", "O", "WELL", "SPG", "DLR", "SBAC"]
  },
  // 바이오테크
  "BIOTECH": {
    sector: "Health Care",
    industry: "Biotechnology",
    subIndustry: "Biotechnology",
    companies: ["AMGN", "GILD", "REGN", "VRTX", "BIIB", "ILMN", "MRNA", "BNTX", "NVAX", "SGEN"]
  },
  // 제약
  "PHARMA": {
    sector: "Health Care",
    industry: "Pharmaceuticals",
    subIndustry: "Pharmaceuticals",
    companies: ["JNJ", "PFE", "MRK", "ABBV", "LLY", "BMY", "RPRX", "ZTS", "VTRS", "JAZZ"]
  },
  // 의료기기
  "MEDICAL_DEVICES": {
    sector: "Health Care",
    industry: "Health Care Equipment & Supplies",
    subIndustry: "Health Care Equipment",
    companies: ["MDT", "ABT", "SYK", "ISRG", "BSX", "EW", "ZBH", "BAX", "HOLX", "GMED"]
  },
  // 은행
  "BANKING": {
    sector: "Financials",
    industry: "Banks",
    subIndustry: "Diversified Banks",
    companies: ["JPM", "BAC", "WFC", "C", "USB", "PNC", "TFC", "FITB", "CFG", "RF"]
  },
  // 보험
  "INSURANCE": {
    sector: "Financials",
    industry: "Insurance",
    subIndustry: "Multi-line Insurance",
    companies: ["AIG", "HIG", "ALL", "TRV", "L", "CINF", "ORI", "AFG", "ACGL", "RNR"]
  },
  // 투자관리
  "INVESTMENT_MANAGEMENT": {
    sector: "Financials",
    industry: "Capital Markets",
    subIndustry: "Asset Management & Custody Banks",
    companies: ["BLK", "BK", "STT", "NTRS", "IVZ", "BEN", "TROW", "AMG", "EV", "JHG"]
  },
  // 클라우드 컴퓨팅
  "CLOUD_COMPUTING": {
    sector: "Information Technology",
    industry: "Software & Services",
    subIndustry: "Cloud Computing",
    companies: ["AMZN", "MSFT", "GOOGL", "ORCL", "IBM", "CRM", "NOW", "WDAY", "TEAM", "ZM"]
  },
  // 사이버보안
  "CYBERSECURITY": {
    sector: "Information Technology",
    industry: "Software & Services",
    subIndustry: "Cybersecurity",
    companies: ["CRWD", "ZS", "PANW", "FTNT", "OKTA", "CYBR", "RPD", "S", "TENB", "VRNS"]
  },
  // 재생에너지
  "RENEWABLE_ENERGY": {
    sector: "Energy",
    industry: "Energy",
    subIndustry: "Renewable Energy",
    companies: ["ENPH", "SEDG", "FSLR", "SPWR", "RUN", "NOVA", "CSIQ", "JKS", "DQ", "ARRY"]
  },
  // 대마초
  "CANNABIS": {
    sector: "Health Care",
    industry: "Pharmaceuticals",
    subIndustry: "Cannabis",
    companies: ["TLRY", "CGC", "ACB", "CRON", "SNDL", "HEXO", "GRWG", "CURLF", "TCNNF", "CRLBF"]
  },
  // 게임
  "GAMING": {
    sector: "Communication Services",
    industry: "Entertainment",
    subIndustry: "Gaming",
    companies: ["ATVI", "EA", "TTWO", "RBLX", "U", "ZNGA", "PLTK", "SCPL", "SKLZ", "SLGG"]
  },
  // 식품 및 음료
  "FOOD_BEVERAGE": {
    sector: "Consumer Staples",
    industry: "Food, Beverage & Tobacco",
    subIndustry: "Packaged Foods & Meats",
    companies: ["PEP", "KHC", "GIS", "K", "CAG", "SJM", "HSY", "CPB", "MKC", "HRL"]
  },
  // 여행 및 레저
  "TRAVEL_LEISURE": {
    sector: "Consumer Discretionary",
    industry: "Hotels, Restaurants & Leisure",
    subIndustry: "Hotels, Resorts & Cruise Lines",
    companies: ["MAR", "HLT", "H", "CCL", "RCL", "NCLH", "WH", "CHH", "TNL", "ABNB"]
  },
  // 교육
  "EDUCATION": {
    sector: "Consumer Discretionary",
    industry: "Diversified Consumer Services",
    subIndustry: "Education Services",
    companies: ["CHGG", "LRN", "STRA", "TWOU", "LINC", "LOPE", "CECO", "PRDO", "UTI", "GPX"]
  },
  // 블록체인 및 암호화폐
  "BLOCKCHAIN_CRYPTO": {
    sector: "Financials",
    industry: "Diversified Financial Services",
    subIndustry: "Blockchain & Cryptocurrency",
    companies: ["COIN", "MSTR", "RIOT", "MARA", "BITF", "HUT", "BTBT", "CLSK", "SI", "NCTY"]
  },
  // 우주
  "SPACE": {
    sector: "Industrials",
    industry: "Aerospace & Defense",
    subIndustry: "Space Exploration",
    companies: ["SPCE", "RKLB", "ASTR", "BKSY", "MNTS", "RDW", "IONQ", "MAXR", "IRDM", "AJRD"]
  },
  // 로봇 및 자동화
  "ROBOTICS_AUTOMATION": {
    sector: "Industrials",
    industry: "Machinery",
    subIndustry: "Robotics & Automation",
    companies: ["ABB", "ROK", "IRBT", "ISRG", "TER", "NNDM", "AVAV", "KTOS", "LDOS", "LHX"]
  },
  // 메타버스
  "METAVERSE": {
    sector: "Communication Services",
    industry: "Interactive Media & Services",
    subIndustry: "Metaverse",
    companies: ["META", "RBLX", "U", "MSFT", "NVDA", "SNAP", "MTCH", "SE", "TTWO", "EA"]
  },
  // 양자 컴퓨팅
  "QUANTUM_COMPUTING": {
    sector: "Information Technology",
    industry: "Software & Services",
    subIndustry: "Quantum Computing",
    companies: ["IBM", "IONQ", "QUBT", "GOOG", "MSFT", "NVDA", "AMZN", "ORCL", "HPE", "INTC"]
  },
  // SPAC
  "SPAC": {
    sector: "Financials",
    industry: "Diversified Financial Services",
    subIndustry: "Special Purpose Acquisition Company",
    companies: []
  },
  // 원자재
  "MATERIALS": {
    sector: "Materials",
    industry: "Materials",
    subIndustry: "Materials",
    companies: ["BHP", "RIO", "VALE", "FCX", "NEM", "DOW", "DD", "LIN", "APD", "ECL"]
  }
};

// 산업군별 기업 목록 로드 (확장 매핑 결과에서 자동 생성)
// 이 부분은 expanded_industries_with_companies.json 파일에서 자동 생성됨

// 심볼-산업군 매핑
export const SYMBOL_TO_INDUSTRY: Record<string, string> = {};

// 산업군 매핑 초기화
for (const [industryKey, group] of Object.entries(INDUSTRY_MAP)) {
  for (const symbol of group.companies) {
    SYMBOL_TO_INDUSTRY[symbol] = industryKey;
  }
}

// 확장 매핑 결과에서 추가 심볼-산업군 매핑 로드
// 이 부분은 expanded_mapping_results.json 파일에서 자동 생성됨

// 산업군 관련 기업 찾기 함수
export function findRelatedCompanies(industryKey: string, limit: number = 5): string[] {
  if (!INDUSTRY_MAP[industryKey]) {
    return [];
  }
  
  return INDUSTRY_MAP[industryKey].companies.slice(0, limit);
}

// 심볼로 산업군 찾기 함수
export function findIndustryBySymbol(symbol: string): string | null {
  return SYMBOL_TO_INDUSTRY[symbol] || null;
}

// 동일 산업군 내 다른 기업 찾기 함수
export function findCompaniesInSameIndustry(symbol: string, limit: number = 5): string[] {
  const industryKey = findIndustryBySymbol(symbol);
  if (!industryKey) {
    return [];
  }
  
  // 동일 산업군 내 다른 기업 찾기
  return INDUSTRY_MAP[industryKey].companies
    .filter(s => s !== symbol)
    .slice(0, limit);
}
