#!/usr/bin/env python3
"""
Debug script to test LSTM service step by step
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path

# Add src/services to path
sys.path.append(str(Path(__file__).parent / "src" / "services"))

def test_data_loading():
    """Test basic data loading"""
    print("Testing data loading...")
    
    try:
        ROOT_DIR = Path(__file__).resolve().parent
        csv_path = ROOT_DIR / "src" / "data" / "sp500_adj_close_3y.csv"
        
        print(f"Looking for file: {csv_path}")
        print(f"File exists: {csv_path.exists()}")
        
        if csv_path.exists():
            df = pd.read_csv(csv_path)
            print(f"File loaded successfully. Shape: {df.shape}")
            print(f"Columns: {list(df.columns)[:10]}...")  # First 10 columns
            
            if 'AAPL' in df.columns:
                print("AAPL column found")
                print(f"AAPL data sample: {df['AAPL'].head()}")
            else:
                print("AAPL column not found")
                
        return True
        
    except Exception as e:
        print(f"Data loading failed: {e}")
        return False

def test_volume_loading():
    """Test volume data loading"""
    print("\nTesting volume data loading...")
    
    try:
        ROOT_DIR = Path(__file__).resolve().parent
        volume_path = ROOT_DIR / "src" / "data" / "sp500_volume_3y.csv"
        
        print(f"Looking for volume file: {volume_path}")
        print(f"Volume file exists: {volume_path.exists()}")
        
        if volume_path.exists():
            df = pd.read_csv(volume_path)
            print(f"Volume file loaded successfully. Shape: {df.shape}")
            
            if 'AAPL' in df.columns:
                print("AAPL volume column found")
                print(f"AAPL volume sample: {df['AAPL'].head()}")
            else:
                print("AAPL volume column not found")
                
        return True
        
    except Exception as e:
        print(f"Volume loading failed: {e}")
        return False

def test_imports():
    """Test TensorFlow imports"""
    print("\nTesting TensorFlow imports...")
    
    try:
        import tensorflow as tf
        print(f"TensorFlow version: {tf.__version__}")
        
        from tensorflow.keras import Sequential
        from tensorflow.keras.layers import Dense, LSTM, Dropout, Input
        print("Keras imports successful")
        
        return True
        
    except Exception as e:
        print(f"TensorFlow import failed: {e}")
        return False

if __name__ == "__main__":
    print("=== LSTM Service Debug Test ===")
    
    # Test data loading
    data_ok = test_data_loading()
    
    # Test volume loading
    volume_ok = test_volume_loading()
    
    # Test TensorFlow imports
    tf_ok = test_imports()
    
    print(f"\n=== Results ===")
    print(f"Data loading: {'✓' if data_ok else '✗'}")
    print(f"Volume loading: {'✓' if volume_ok else '✗'}")
    print(f"TensorFlow imports: {'✓' if tf_ok else '✗'}")
    
    if all([data_ok, volume_ok, tf_ok]):
        print("\nAll tests passed! The issue might be in the LSTM service logic.")
    else:
        print("\nSome tests failed. Fix these issues first.")
