"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/lstm_data";
exports.ids = ["pages/api/lstm_data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_data.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_data.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_lstm_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\lstm_data.ts */ \"(api)/./src/pages/api/lstm_data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_lstm_data_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/lstm_data\",\n        pathname: \"/api/lstm_data\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_lstm_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmxzdG1fZGF0YSZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnNyYyU1Q3BhZ2VzJTVDYXBpJTVDbHN0bV9kYXRhLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQzREO0FBQzVEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyx3REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsd0RBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9zcmNcXFxccGFnZXNcXFxcYXBpXFxcXGxzdG1fZGF0YS50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCAnZGVmYXVsdCcpO1xuLy8gUmUtZXhwb3J0IGNvbmZpZy5cbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgJ2NvbmZpZycpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbHN0bV9kYXRhXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvbHN0bV9kYXRhXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogJycsXG4gICAgICAgIGZpbGVuYW1lOiAnJ1xuICAgIH0sXG4gICAgdXNlcmxhbmRcbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1wYWdlcy1hcGkuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_data.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/lstm_data.ts":
/*!************************************!*\
  !*** ./src/pages/api/lstm_data.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! child_process */ \"child_process\");\n/* harmony import */ var child_process__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(child_process__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// src/pages/api/lstm_data.ts\n\n\n/**\n * API endpoint for AI chat to access LSTM prediction data\n * \n * GET /api/lstm_data?symbol=AAPL - Get LSTM data for specific symbol\n * GET /api/lstm_data?action=list - Get list of available symbols\n * GET /api/lstm_data?symbol=AAPL&format=summary - Get AI-friendly summary\n */ async function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            success: false,\n            error: 'Method not allowed. Use GET.'\n        });\n    }\n    const { symbol, action, format } = req.query;\n    try {\n        // Handle list action - get all available symbols\n        if (action === 'list') {\n            const symbols = await getAvailableSymbols();\n            return res.status(200).json({\n                success: true,\n                available_symbols: symbols\n            });\n        }\n        // Validate symbol parameter\n        if (!symbol || typeof symbol !== 'string') {\n            return res.status(400).json({\n                success: false,\n                error: 'Symbol parameter is required'\n            });\n        }\n        const symbolUpper = symbol.toUpperCase();\n        // Get LSTM data for symbol\n        const lstmData = await getLSTMData(symbolUpper);\n        if (!lstmData) {\n            return res.status(404).json({\n                success: false,\n                error: `No LSTM data found for symbol ${symbolUpper}`\n            });\n        }\n        // Handle summary format for AI consumption\n        if (format === 'summary') {\n            const summary = lstmData.analysis?.ai_summary || 'No summary available';\n            return res.status(200).json({\n                success: true,\n                data: {\n                    ...lstmData,\n                    analysis: {\n                        ...lstmData.analysis,\n                        ai_summary: summary\n                    }\n                }\n            });\n        }\n        // Return full data\n        return res.status(200).json({\n            success: true,\n            data: lstmData\n        });\n    } catch (error) {\n        console.error('LSTM data API error:', error);\n        return res.status(500).json({\n            success: false,\n            error: 'Internal server error while retrieving LSTM data'\n        });\n    }\n}\n/**\n * Get LSTM data for a specific symbol using Python data store\n */ async function getLSTMData(symbol) {\n    return new Promise((resolve, reject)=>{\n        const pythonScript = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services', 'lstm_data_store.py');\n        // Create a simple Python script call to get data\n        const servicesPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services').replace(/\\\\/g, '/');\n        const pythonCode = `\nimport sys\nimport os\nsys.path.append('${servicesPath}')\n\ntry:\n    from lstm_data_store import get_lstm_result\n    import json\n\n    result = get_lstm_result('${symbol}')\n    if result:\n        print(json.dumps(result))\n    else:\n        print('null')\nexcept Exception as e:\n    print(f'ERROR: {e}', file=sys.stderr)\n    print('null')\n`;\n        const python = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', [\n            '-c',\n            pythonCode\n        ], {\n            cwd: process.cwd(),\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ]\n        });\n        let stdout = '';\n        let stderr = '';\n        python.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        python.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        python.on('close', (code)=>{\n            if (code !== 0) {\n                console.error('Python script error:', stderr);\n                resolve(null);\n                return;\n            }\n            try {\n                const result = JSON.parse(stdout.trim());\n                resolve(result === null ? null : result);\n            } catch (error) {\n                console.error('JSON parse error:', error);\n                resolve(null);\n            }\n        });\n        python.on('error', (error)=>{\n            console.error('Python spawn error:', error);\n            resolve(null);\n        });\n    });\n}\n/**\n * Get list of available symbols with LSTM data\n */ async function getAvailableSymbols() {\n    return new Promise((resolve, reject)=>{\n        const servicesPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'src', 'services').replace(/\\\\/g, '/');\n        const pythonCode = `\nimport sys\nimport os\nsys.path.append('${servicesPath}')\n\ntry:\n    from lstm_data_store import get_lstm_store\n    import json\n\n    store = get_lstm_store()\n    symbols = store.get_all_symbols()\n    print(json.dumps(symbols))\nexcept Exception as e:\n    print(f'ERROR: {e}', file=sys.stderr)\n    print('[]')\n`;\n        const python = (0,child_process__WEBPACK_IMPORTED_MODULE_0__.spawn)('python', [\n            '-c',\n            pythonCode\n        ], {\n            cwd: process.cwd(),\n            stdio: [\n                'pipe',\n                'pipe',\n                'pipe'\n            ]\n        });\n        let stdout = '';\n        let stderr = '';\n        python.stdout.on('data', (data)=>{\n            stdout += data.toString();\n        });\n        python.stderr.on('data', (data)=>{\n            stderr += data.toString();\n        });\n        python.on('close', (code)=>{\n            if (code !== 0) {\n                console.error('Python script error:', stderr);\n                resolve([]);\n                return;\n            }\n            try {\n                const symbols = JSON.parse(stdout.trim());\n                resolve(Array.isArray(symbols) ? symbols : []);\n            } catch (error) {\n                console.error('JSON parse error:', error);\n                resolve([]);\n            }\n        });\n        python.on('error', (error)=>{\n            console.error('Python spawn error:', error);\n            resolve([]);\n        });\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/lstm_data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Flstm_data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Clstm_data.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();