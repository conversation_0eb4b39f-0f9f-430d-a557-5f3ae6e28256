#!/usr/bin/env python3
"""
Test data loading for LSTM service
"""

import pandas as pd
from pathlib import Path

def test_data_loading():
    """Test the data loading approach from lstm_finetuning.py"""
    try:
        ROOT_DIR = Path(__file__).resolve().parent
        data_path = lambda name: ROOT_DIR / 'src' / 'data' / f'sp500_{name}_3y.csv'
        
        ticker = 'AAPL'
        df = pd.DataFrame()
        all_cols = ['open', 'high', 'low', 'close', 'adj_close', 'volume']
        
        print("Testing data loading...")
        
        for col_name in all_cols:
            file_path = data_path(col_name)
            print(f"Checking file: {file_path}")
            print(f"File exists: {file_path.exists()}")
            
            if file_path.exists():
                try:
                    temp_df = pd.read_csv(file_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date')
                    df[col_name.replace('_', ' ').title().replace(' ', '')] = temp_df[ticker]
                    print(f"  Loaded {col_name}: {len(temp_df)} rows")
                except Exception as e:
                    print(f"  Error loading {col_name}: {e}")
            else:
                print(f"  File not found: {file_path}")
        
        print(f"\nFinal DataFrame shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        
        if not df.empty:
            df.rename(columns={'AdjClose': 'Adj Close'}, inplace=True)
            df.dropna(inplace=True)
            print(f"After cleanup: {df.shape}")
            print(f"Date range: {df.index.min()} to {df.index.max()}")
            print("Data loading successful!")
            return True
        else:
            print("No data loaded!")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    test_data_loading()
