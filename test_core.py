#!/usr/bin/env python3
"""
Direct test of core LSTM service functionality.
"""

def test_traffic_light():
    """Test the traffic light classification function."""
    def get_traffic_light(prediction):
        if prediction > 0.525:  # Strong buy signal
            return 'GREEN'
        elif prediction < 0.475:  # Strong sell signal
            return 'RED'
        else:  # Neutral signal
            return 'YELLOW'
    
    # Test cases
    print("Testing traffic light classification:")
    test_cases = [
        (0.6, 'GREEN'),
        (0.4, 'RED'),
        (0.5, 'Y<PERSON>LOW'),
        (0.53, 'GREEN'),
        (0.47, 'RED')
    ]
    
    for value, expected in test_cases:
        result = get_traffic_light(value)
        status = "✅" if result == expected else "❌"
        print(f"{status} {value:.3f} -> {result} (expected: {expected})")

if __name__ == "__main__":
    print("Testing LSTM Service Core Functionality")
    print("=" * 60)
    test_traffic_light()
    print("\nTest completed!")
