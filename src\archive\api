// src/pages/api/chart_data.ts
console.log('AV-KEY', process.env.ALPHA_VANTAGE_API_KEY?.slice(0, 6)); // 앞 6글자만 확인용

import type { NextApiRequest, NextApiResponse } from 'next';

/* Alpha Vantage 응답 타입(필수 필드만) */
interface AVDaily {
  'Time Series (Daily)'?: Record<string, {
    '1. open':  string;
    '2. high':  string;
    '3. low':   string;
    '4. close': string;
    '5. volume':string;
  }>;
  Note?: string;
  Information?: string;
  'Error Message'?: string;
}

type Series = {
  timestamp: number[]; open: number[]; high: number[];
  low: number[];       close: number[]; volume: number[];
};
type Err = { error: string };

const API_KEY = process.env.ALPHA_VANTAGE_API_KEY!; // .env.local 필수

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Series | Err>,
) {
  const { symbol } = req.query;
  if (!symbol || Array.isArray(symbol)) {
    return res.status(400).json({ error: 'symbol 파라미터가 필요합니다.' });
  }

  try {
    /* Alpha Vantage 호출 */
    const url = `https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=${encodeURIComponent(
      symbol as string,
    )}&outputsize=compact&apikey=${API_KEY}`;

    const av = (await fetch(url).then(r => r.json())) as AVDaily;

    /* ───── 디버그 로그 (필요 시 삭제) ───── */
    console.dir(av, { depth: 1 });
    console.log(
      'fieldCheck:',
      'TS', !!av['Time Series (Daily)'],
      'Note', !!av.Note,
      'Info', !!av.Information,
      'ErrMsg', !!av['Error Message'],
    );
    /* ─────────────────────────────────────── */

    /* 1️⃣ 정상 데이터 */
    if (av['Time Series (Daily)']) {
      const obj   = av['Time Series (Daily)']!;
      const dates = Object.keys(obj).sort((a, b) => +new Date(a) - +new Date(b));

      const s: Series = {
        timestamp: [], open: [], high: [], low: [], close: [], volume: [],
      };

      for (const d of dates) {
        const v = obj[d]!;
        s.timestamp.push((+new Date(d) / 1000) | 0);
        s.open.push(+v['1. open']);
        s.high.push(+v['2. high']);
        s.low.push(+v['3. low']);
        s.close.push(+v['4. close']);
        s.volume.push(+v['5. volume']);
      }
      return res.status(200).json(s);
    }

    /* 2️⃣ 호출 제한 (429) */
    if (av.Note) {
      console.warn('[AV] rate-limit');
      return res.status(429).json({
        error: 'Alpha Vantage 호출 제한(분당 5회/하루 500회)을 초과했습니다. 1분 후 다시 시도하세요.',
      });
    }

    /* 3️⃣ 심볼 또는 키 오류 (404) */
    if (av.Information || av['Error Message']) {
      console.warn('[AV] invalid symbol or key');
      return res.status(404).json({ error: '유효하지 않은 심볼이거나 API 키 오류입니다.' });
    }

    /* 4️⃣ 알 수 없는 예외 */
    throw new Error('unknown error from Alpha Vantage');
  } catch (e: any) {
    console.error('[chart_data] err', e);
    return res.status(500).json({ error: e.message || '서버 오류' });
  }
}