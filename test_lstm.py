#!/usr/bin/env python3
"""
Simple test script for LSTM service.
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, date, timedelta
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

# Import the LSTM service
from src.services.lstm_service_clean import (
    compute_technical_indicators,
    create_sequences,
    build_lstm_model,
    get_traffic_light,
    REFERENCE_DATE
)

def test_technical_indicators():
    """Test technical indicator calculations."""
    print("Testing technical indicators...")
    
    # Create sample data
    np.random.seed(42)
    dates = pd.date_range(end=REFERENCE_DATE, periods=100, freq='B')
    df = pd.DataFrame({
        'close': np.cumprod(1 + np.random.normal(0.001, 0.02, 100)) * 100,
        'volume': np.abs(np.random.normal(1000000, 200000, 100)).astype(int)
    }, index=dates)
    
    # Compute indicators
    df = compute_technical_indicators(df)
    
    # Check that all expected columns are present
    expected_cols = ['close', 'volume', 'rsi', 'bb_upper_pct', 'bb_lower_pct', 
                    'bb_width_pct', 'volatility_30d', 'log_return']
    
    for col in expected_cols:
        assert col in df.columns, f"Missing column: {col}"
    
    print("✅ Technical indicators test passed")
    return df

def test_sequence_creation():
    """Test sequence creation for LSTM."""
    print("\nTesting sequence creation...")
    
    # Create sample data
    np.random.seed(42)
    X = np.random.rand(100, 5)  # 100 samples, 5 features
    
    # Create sequences
    look_back = 10
    n_features = 4  # Last column is target
    X_seq, y_seq = create_sequences(X, look_back, n_features)
    
    # Check shapes
    assert X_seq.shape == (90, look_back, n_features), "X shape mismatch"
    assert y_seq.shape == (90,), "y shape mismatch"
    
    print("✅ Sequence creation test passed")
    return X_seq, y_seq

def test_traffic_light():
    """Test traffic light classification."""
    print("\nTesting traffic light classification...")
    
    # Test cases
    assert get_traffic_light(0.6) == 'GREEN', "Should be GREEN"
    assert get_traffic_light(0.4) == 'RED', "Should be RED"
    assert get_traffic_light(0.5) == 'YELLOW', "Should be YELLOW"
    
    print("✅ Traffic light classification test passed")

if __name__ == "__main__":
    print(f"Testing LSTM Service - Reference Date: {REFERENCE_DATE}")
    print("=" * 60)
    
    # Run tests
    test_technical_indicators()
    test_sequence_creation()
    test_traffic_light()
    
    print("\nAll tests passed!")
