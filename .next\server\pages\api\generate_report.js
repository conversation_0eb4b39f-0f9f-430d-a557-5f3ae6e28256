"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/generate_report";
exports.ids = ["pages/api/generate_report"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = import("openai");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate_report&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cgenerate_report.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate_report&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cgenerate_report.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_generate_report_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\generate_report.ts */ \"(api)/./src/pages/api/generate_report.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_generate_report_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_generate_report_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_generate_report_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_generate_report_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/generate_report\",\n        pathname: \"/api/generate_report\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _src_pages_api_generate_report_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate_report&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cgenerate_report.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/generate_report.ts":
/*!******************************************!*\
  !*** ./src/pages/api/generate_report.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"openai\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([openai__WEBPACK_IMPORTED_MODULE_0__]);\nopenai__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\nasync function handler(req, res) {\n    if (req.method !== 'POST') {\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    }\n    try {\n        const reportData = req.body;\n        if (!reportData.symbol) {\n            return res.status(400).json({\n                error: 'Symbol is required'\n            });\n        }\n        // Format the input data following the pattern from finetuning_collect.py\n        const formatTrafficLight = (light)=>light.toUpperCase();\n        let userMessage = '';\n        if (reportData.rsi) {\n            userMessage += `[RSI Value: ${reportData.rsi.value.toFixed(2)}, Traffic light: ${formatTrafficLight(reportData.rsi.traffic_light)}] `;\n        }\n        if (reportData.bollinger) {\n            userMessage += `[Bollinger %B: ${reportData.bollinger.value.toFixed(2)}, Traffic light: ${formatTrafficLight(reportData.bollinger.traffic_light)}] `;\n        }\n        if (reportData.mfi) {\n            userMessage += `[MFI Value: ${reportData.mfi.value.toFixed(2)}, Traffic light: ${formatTrafficLight(reportData.mfi.traffic_light)}] `;\n        }\n        if (reportData.capm) {\n            userMessage += `[Market Beta: ${reportData.capm.beta.toFixed(2)}, R²: ${reportData.capm.r2.toFixed(2)}, t-stat: ${reportData.capm.tstat.toFixed(2)}, Traffic light: ${formatTrafficLight(reportData.capm.traffic_light)}] `;\n        }\n        if (reportData.garch) {\n            userMessage += `[Volatility: ${reportData.garch.volatility.toFixed(1)}%, VaR 95%: ${reportData.garch.var95.toFixed(1)}%, Traffic light: ${formatTrafficLight(reportData.garch.traffic_light)}] `;\n        }\n        if (reportData.industry) {\n            userMessage += `[Industry Beta: ${reportData.industry.beta.toFixed(2)}, R²: ${reportData.industry.r2.toFixed(2)}, t-stat: ${reportData.industry.tstat.toFixed(2)}, Traffic light: ${formatTrafficLight(reportData.industry.traffic_light)}] `;\n            userMessage += `[industry : ${reportData.industry.industry_name}] `;\n        }\n        if (reportData.lstm) {\n            userMessage += `[LSTM accuracy: ${reportData.lstm.accuracy.toFixed(3)}, Prediction probability up: ${reportData.lstm.pred_prob_up.toFixed(3)}, Traffic light: ${formatTrafficLight(reportData.lstm.traffic_light)}]`;\n        }\n        console.log(`[REPORT_API] Generating report for ${reportData.symbol} with message: ${userMessage}`);\n        // Call the fine-tuned model\n        const response = await openai.chat.completions.create({\n            model: 'ft:gpt-4.1-mini-2025-04-14:personal:trafficanalysis:BlIDQnBQ:ckpt-step-1810',\n            messages: [\n                {\n                    role: 'system',\n                    content: '당신은 이 메시지를 바탕으로 투자 의견을 제공하는 투자 AI입니다. 이모티콘을 적절히 활용해 친근한 분위기를 유지하면서(최대 2개까지, 💪🚀✨💎📈🎯💡🔥⭐️🌟💫🎉🎂 등), 전문적인 어조로 답변하세요'\n                },\n                {\n                    role: 'user',\n                    content: userMessage\n                }\n            ],\n            temperature: 1.0,\n            max_tokens: 2048\n        });\n        const report = response.choices[0].message.content?.trim();\n        if (!report) {\n            throw new Error('No report generated');\n        }\n        console.log(`[REPORT_API] Report generated successfully for ${reportData.symbol}`);\n        res.status(200).json({\n            success: true,\n            report,\n            symbol: reportData.symbol,\n            companyName: reportData.companyName\n        });\n    } catch (error) {\n        console.error('[REPORT_API] Error generating report:', error);\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        res.status(500).json({\n            error: 'Failed to generate report',\n            message: errorMessage\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/generate_report.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fgenerate_report&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cgenerate_report.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();