#!/usr/bin/env python3
import sys
"""
Intel-Optimized LSTM Stock Prediction Service for Fine-tuning
6-feature input (adj close, volume, RSI-14, <PERSON><PERSON>er %) with 75-day window / 5-day horizon
"""

import os
import sys
import json
import random
import re
import argparse
from datetime import date, timedelta, datetime
from pathlib import Path

# Disable oneDNN optimizations to prevent crashes
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports with Intel optimizations
import tensorflow as tf
tf.get_logger().setLevel('ERROR')



from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import (
    Input, LSTM, Dense, Dropout, Bidirectional, SpatialDropout1D, Activation, dot, Softmax, Flatten, <PERSON>er<PERSON><PERSON>alization, Gaussian<PERSON>oise, Lambda
)
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import AdamW
from tensorflow.keras.callbacks import EarlyStopping



import numpy as np
import pandas as pd
from pandas.tseries.offsets import BDay
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import joblib

# Common Constants for Technical Indicator-based LSTM Prediction
WINDOW_DAYS = 75        # LSTM input sequence length (75 trading days)
HORIZON_DAYS = 5        # Prediction horizon (5 business days ahead)
RSI_PERIOD = 14         # RSI calculation period
BB_PERIOD = 20          # Bollinger Bands period
BB_STD_MULT = 2         # Bollinger Bands standard deviation multiplier
FEATURE_COLS = [
    'Adj Close', 'Volume',
    'rsi14', 'bb_upper_pct',
    'bb_lower_pct', 'bb_width_pct'
]


def compute_indicators(df):
    """
    Compute technical indicators using original column names.
    The input DataFrame must have a DatetimeIndex and 'Adj Close', 'Volume' columns.
    """
    df = df.sort_index().copy()

    # Calculate RSI(14)
    delta = df['Adj Close'].diff()
    gain = delta.clip(lower=0)
    loss = -delta.clip(upper=0)
    avg_gain = gain.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    avg_loss = loss.ewm(alpha=1/RSI_PERIOD, min_periods=RSI_PERIOD).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    df['rsi14'] = rsi / 100.0

    # Calculate Bollinger Bands
    sma = df['Adj Close'].rolling(window=BB_PERIOD).mean()
    std = df['Adj Close'].rolling(window=BB_PERIOD).std()
    bb_upper = sma + (BB_STD_MULT * std)
    bb_lower = sma - (BB_STD_MULT * std)
    df['bb_upper_pct'] = ((df['Adj Close'] - bb_upper) / bb_upper * 100).clip(lower=-100, upper=100)
    df['bb_lower_pct'] = ((df['Adj Close'] - bb_lower) / bb_lower * 100).clip(lower=-100, upper=100)
    df['bb_width_pct'] = ((bb_upper - bb_lower) / sma * 100).clip(lower=0, upper=100)

    # Define target variable
    df['target'] = (df['Adj Close'].shift(-HORIZON_DAYS) > df['Adj Close']).astype(int)
    
    return df.dropna()


def adjust_to_trading_day(ts, df):
    if ts not in df.index:
        ts = ts - BDay(1)
        while ts not in df.index:
            ts -= BDay(1)
    return ts





def load_and_prepare_data(ticker, target_date_str):
    """
    Loads 3.5 years of data, splits into training and evaluation sets based on target date logic.
    Target Date Logic:
    - target_date_str becomes "Day 0"
    - Training data includes all data up to and including "Day -5" (5 days before target date)
    - Evaluation data includes days -4, -3, -2, -1, and 0 for accuracy calculation
    - Also predict "Day +1" for traffic light signal
    """
    try:
        ROOT_DIR = Path(__file__).resolve().parents[1]

        # Load price data with explicit dtype to handle numeric columns properly
        price_path = ROOT_DIR / 'data' / 'sp500_adj_close_3y.csv'
        price_data = pd.read_csv(price_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date', dtype={ticker: 'float64'})
        price_data.rename(columns={ticker: 'Adj Close'}, inplace=True)

        # Load volume data with explicit dtype
        volume_path = ROOT_DIR / 'data' / 'sp500_volume_3y.csv'
        volume_data = pd.read_csv(volume_path, usecols=['Date', ticker], parse_dates=['Date'], index_col='Date', dtype={ticker: 'float64'})
        volume_data.rename(columns={ticker: 'Volume'}, inplace=True)

        # Merge and clean data
        all_data = pd.merge(price_data, volume_data, left_index=True, right_index=True, how='inner').dropna()
        all_data.sort_index(inplace=True)

        # Ensure datetime index has no time component (date only)
        all_data.index = pd.to_datetime(all_data.index.date)

        if all_data.empty:
            raise ValueError(f"No data for {ticker}.")

        # Adjust target date to trading day (Day 0)
        target_date = adjust_to_trading_day(pd.to_datetime(target_date_str), all_data)

        # Training data: up to and including Day -5
        train_end_date = target_date - BDay(5)  # Day -5 (inclusive)
        train_start_date = train_end_date - pd.DateOffset(years=3, months=6)  # 3.5 years of training data
        train_data = all_data.loc[train_start_date:train_end_date]

        # Evaluation data: Days -4, -3, -2, -1, 0 for accuracy calculation
        eval_start_date = target_date - BDay(4)  # Day -4
        eval_end_date = target_date  # Day 0
        eval_data = all_data.loc[eval_start_date:eval_end_date]

        if train_data.empty:
            raise ValueError(f"No training data available for {ticker} ending on {train_end_date.date()}")

        if eval_data.empty or len(eval_data) < 5:
            raise ValueError(f"Not enough evaluation data for {ticker} from {eval_start_date.date()} to {eval_end_date.date()}")

        print(f"Data loaded: Training from {train_data.index.min().date()} to {train_data.index.max().date()} ({len(train_data)} days)")
        print(f"Evaluation from {eval_data.index.min().date()} to {eval_data.index.max().date()} ({len(eval_data)} days)")

        return all_data, train_data, eval_data, target_date.date()

    except FileNotFoundError as e:
        raise ValueError(f"Data file not found: {e}")
    except KeyError:
        raise ValueError(f"Data for ticker '{ticker}' not found.")


def create_sequences(df, feature_cols):

    X, y = [], []

    for i in range(len(df) - WINDOW_DAYS - HORIZON_DAYS):
        # Extract sequence from df.iloc[i:i+WINDOW_DAYS, feature_cols]
        sequence = df.iloc[i:i+WINDOW_DAYS][feature_cols].values

        X.append(sequence)

        # Calculate target index: t = i + WINDOW_DAYS - 1, target_date = t + HORIZON_DAYS
        t = i + WINDOW_DAYS - 1
        target_date = t + HORIZON_DAYS

        # Create binary label: 1 if price[target_date] > price[t], else 0
        current_price = df.iloc[t][feature_cols[0]]  # Use first feature column (price)
        future_price = df.iloc[target_date][feature_cols[0]]  # Use first feature column (price)
        y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def focal_loss(gamma=2.0):
    """Focal Loss to address class imbalance (no class weights)"""
    def loss(y_true, y_pred):
        bce = tf.keras.losses.binary_crossentropy(y_true, y_pred)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        return tf.pow(1 - p_t, gamma) * bce
    return loss


def build_lstm_model(input_shape=(WINDOW_DAYS, len(FEATURE_COLS))):
    """
    Build advanced LSTM classifier model based on the new specifications.

    Architecture: LayerNorm -> GaussianNoise -> Bi-LSTM -> SpatialDropout -> Attention -> Dense Head
    Optimizer: AdamW(3e-4, weight_decay=1e-4)
    """
    # Lightweight Attention Layer
    def lightweight_attention(inputs):
        # inputs shape: (batch_size, sequence_length, features)
        x = Dense(16, activation='tanh', name='attention_dense_1')(inputs)
        x = Dense(1, name='attention_dense_2')(x)
        attention_weights = Softmax(axis=1, name='attention_softmax')(x)
        # Weighted sum
        context_vector = Lambda(lambda t: tf.reduce_sum(t, axis=1), name='attention_sum')(attention_weights * inputs)
        return context_vector

    # Model Definition
    inp = Input(shape=input_shape, name='input_layer')
    x = LayerNormalization(name='layer_norm')(inp)
    x = GaussianNoise(0.05, name='gaussian_noise')(x)
    x = Bidirectional(LSTM(48, return_sequences=True), name='bidirectional_lstm')(x)
    x = SpatialDropout1D(0.15, name='spatial_dropout')(x)
    
    # Attention Mechanism
    attn_output = lightweight_attention(x)

    # Dense Head
    x = Dense(24, activation='relu', name='dense_24')(attn_output)
    output = Dense(1, activation='sigmoid', name='output_layer')(x)

    model = Model(inputs=inp, outputs=output)

    # Compile with AdamW optimizer
    optimizer = AdamW(learning_rate=3e-4, weight_decay=1e-4)
    model.compile(optimizer=optimizer, loss='binary_crossentropy', metrics=['accuracy'])

    return model


def update_history_csv(ticker, prediction_date, is_correct):
    """
    Update traffic light history CSV (keep last 5 rows)

    Parameters:
    -----------
    ticker : str
        Stock ticker symbol
    prediction_date : date or Timestamp
        Date of the prediction
    is_correct : int
        1 if prediction was correct, 0 if incorrect
        
    Returns:
    --------
    pandas.DataFrame
        DataFrame containing the updated history with columns ['date', 'is_correct']
    """
    # Create traffic_history directory if it doesn't exist
    ROOT_DIR = Path(__file__).resolve().parents[1]
    history_dir = ROOT_DIR / "data" / "traffic_history"
    history_dir.mkdir(parents=True, exist_ok=True)

    # Path to ticker's history file
    history_file = history_dir / f"{ticker}.csv"

    # Convert prediction_date to date object for consistency
    if hasattr(prediction_date, 'date'):
        prediction_date = prediction_date.date()

    # Load existing history or create new DataFrame
    if history_file.exists():
        try:
            history_df = pd.read_csv(history_file)
            history_df['date'] = pd.to_datetime(history_df['date']).dt.date
        except Exception as e:
            print(f"Error reading history file {history_file}: {e}", file=sys.stderr)
            history_df = pd.DataFrame(columns=['date', 'is_correct'])
    else:
        history_df = pd.DataFrame(columns=['date', 'is_correct'])

    # Add new record
    new_record = pd.DataFrame({
        'date': [prediction_date],
        'is_correct': [is_correct]
    })
    history_df = pd.concat([history_df, new_record], ignore_index=True)

    # Keep only the last 5 records
    history_df = history_df.tail(5)


    # Save updated history
    history_df.to_csv(history_file, index=False)
    
    return history_df

def get_actual_label_if_available(df, date):
    """
    Safely retrieves the actual movement label for a given date if future data is available.
    
    Parameters:
    -----------
    df : pd.DataFrame
        DataFrame containing at least 'Adj Close' and a DatetimeIndex.
    date : pd.Timestamp
        The date for which to find the future label.
        
    Returns:
    --------
    int or None
        Returns 1 if the price went up, 0 if it went down.
        Returns None if the future date is not in the DataFrame.
    """
    future_date = pd.to_datetime(date) + BDay(HORIZON_DAYS)
    
    # Ensure both the prediction date and the future date are in the index
    if future_date in df.index and pd.to_datetime(date) in df.index:
        actual_future_price = df.loc[future_date, 'Adj Close']
        price_on_date = df.loc[pd.to_datetime(date), 'Adj Close']
        return 1 if actual_future_price > price_on_date else 0
    
    # Return None if future data is not available
    return None


def main(ticker, target_date_str):
    # 1. 데이터 로드 및 준비 (Load and prepare data)
    try:
        all_data, train_data, eval_data, adjusted_target_date = load_and_prepare_data(ticker, target_date_str)
    except ValueError as e:
        print(f"Error: {e}", file=sys.stderr)
        return

    # 2. 기술적 지표 계산 (Compute technical indicators)
    train_indicators = compute_indicators(train_data)
    all_indicators = compute_indicators(all_data)

    # 3. 데이터 스케일링 (Scale data)
    # Fit scaler on training data ONLY to prevent data leakage
    scaler = MinMaxScaler()
    scaler.fit(train_indicators[FEATURE_COLS])

    # Transform the entire dataset using the fitted scaler
    scaled_indicators = scaler.transform(all_indicators[FEATURE_COLS])
    scaled_indicators_df = pd.DataFrame(scaled_indicators, columns=FEATURE_COLS, index=all_indicators.index)
    scaled_indicators_df['target'] = all_indicators['target'] # Add target column back

    # 4. 학습용 시퀀스 데이터 생성 (Create training sequences)
    # Use only the training data portion for sequence creation
    # Find the intersection of indices to avoid KeyError
    train_indices = train_data.index.intersection(scaled_indicators_df.index)
    if len(train_indices) == 0:
        print("Error: No overlapping indices between training data and scaled indicators.", file=sys.stderr)
        return

    train_scaled_data = scaled_indicators_df.loc[train_indices]
    X_train, y_train = create_sequences(train_scaled_data, FEATURE_COLS)

    if X_train.shape[0] == 0:
        print("Error: Not enough training data to create sequences.", file=sys.stderr)
        return

    # 5. 모델 빌드 및 학습 (Build and train model)
    model = build_lstm_model(input_shape=(X_train.shape[1], X_train.shape[2]))
    early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)
    # verbose=0 to keep the output clean during execution
    model.fit(X_train, y_train, epochs=50, batch_size=32, validation_split=0.1, callbacks=[early_stopping], verbose=0)

    # 6. 평가 (Evaluate on Days -4, -3, -2, -1, 0 for accuracy calculation)
    eval_predictions = []
    eval_true_labels = []
    eval_dates_processed = []

    # Process each evaluation day (Days -4 to 0)
    for eval_date in eval_data.index:
        try:
            # Create sequence from data *before* the evaluation date
            # The sequence should end on the day before the prediction date
            sequence_end_date = eval_date - BDay(1)
            sequence_start_date = sequence_end_date - BDay(WINDOW_DAYS - 1)

            # Get sequence data with proper index alignment
            sequence_indices = pd.date_range(start=sequence_start_date, end=sequence_end_date, freq='B')
            available_indices = sequence_indices.intersection(scaled_indicators_df.index)

            if len(available_indices) < WINDOW_DAYS:
                print(f"Warning: Not enough data for sequence ending {sequence_end_date.date()}, skipping evaluation for {eval_date.date()}")
                continue

            # Take the last WINDOW_DAYS available data points
            sequence_data = scaled_indicators_df.loc[available_indices[-WINDOW_DAYS:]]

            if len(sequence_data) != WINDOW_DAYS:
                print(f"Warning: Sequence length {len(sequence_data)} != {WINDOW_DAYS} for {eval_date.date()}, skipping")
                continue

            X_eval = np.expand_dims(sequence_data[FEATURE_COLS].values, axis=0)

            # Predict and store results
            pred_prob = model.predict(X_eval, verbose=0)[0][0]
            eval_predictions.append(int(pred_prob >= 0.5))

            # Get true label if available
            if eval_date in scaled_indicators_df.index:
                eval_true_labels.append(scaled_indicators_df.loc[eval_date, 'target'])
                eval_dates_processed.append(eval_date)
            else:
                print(f"Warning: No target data available for {eval_date.date()}")

        except Exception as e:
            print(f"Error processing evaluation for {eval_date.date()}: {e}")
            continue

    # 7. 평가 지표 계산 (Calculate evaluation metrics)
    if eval_true_labels and len(eval_true_labels) == len(eval_predictions):
        metrics = {
            'accuracy': accuracy_score(eval_true_labels, eval_predictions),
            'precision': precision_score(eval_true_labels, eval_predictions, zero_division=0),
            'recall': recall_score(eval_true_labels, eval_predictions, zero_division=0),
            'f1': f1_score(eval_true_labels, eval_predictions, zero_division=0)
        }
        print(f"Evaluation on {len(eval_dates_processed)} days ({eval_data.index.min().date()} ~ {eval_data.index.max().date()}) metrics: {metrics}")
    else:
        metrics = {'accuracy': 0, 'precision': 0, 'recall': 0, 'f1': 0}
        print("Warning: No valid evaluation data available for metrics calculation")

    # 8. Day +1 예측 (Predict for Day +1 for traffic light)
    # Create sequence ending on target date (Day 0) to predict Day +1
    try:
        final_sequence_end_date = pd.to_datetime(adjusted_target_date)
        final_sequence_start_date = final_sequence_end_date - BDay(WINDOW_DAYS - 1)

        # Get sequence data with proper index alignment
        final_sequence_indices = pd.date_range(start=final_sequence_start_date, end=final_sequence_end_date, freq='B')
        final_available_indices = final_sequence_indices.intersection(scaled_indicators_df.index)

        if len(final_available_indices) < WINDOW_DAYS:
            print(f"Warning: Not enough data for final prediction sequence, using last {len(final_available_indices)} available days")
            final_sequence_data = scaled_indicators_df.loc[final_available_indices]
        else:
            # Take the last WINDOW_DAYS available data points
            final_sequence_data = scaled_indicators_df.loc[final_available_indices[-WINDOW_DAYS:]]

        if len(final_sequence_data) == 0:
            raise ValueError("No data available for final prediction")

        X_pred_final = np.expand_dims(final_sequence_data[FEATURE_COLS].values, axis=0)
        final_prob = model.predict(X_pred_final, verbose=0)[0][0]

        print(f"Final prediction for Day +1: probability = {final_prob:.4f}")

    except Exception as e:
        print(f"Error in final prediction: {e}")
        final_prob = 0.5  # Default neutral probability

    # 9. 동적 보정 (Dynamic calibration)
    recent_vol = all_indicators.loc[:adjusted_target_date]['Adj Close'].pct_change().rolling(20).std().iloc[-1]
    annual_vol = all_indicators.loc[:adjusted_target_date]['Adj Close'].pct_change().rolling(252).std().iloc[-1]
    high_vol = recent_vol > annual_vol * 1.5

    alpha, cutoff = (0.15, 0.35) if high_vol else (0.30, 0.50)
    calibration_method = 'high_vol' if high_vol else 'normal'
    calibrated_prob = (1 - alpha) * final_prob + alpha * 0.5
    predicted_direction = int(calibrated_prob >= cutoff)

    # 10. 이력 업데이트 및 신호등 생성 (Update history and create traffic light signal)
    actual_direction = get_actual_label_if_available(all_data, adjusted_target_date)
    is_correct = 1 if predicted_direction == actual_direction else 0 if actual_direction is not None else -1
    hits_df = update_history_csv(ticker, adjusted_target_date, is_correct)
    hits = hits_df['is_correct'].sum() if hits_df is not None else 0

    signal_colour = "green" if hits >= 4 else "yellow" if hits == 3 else "red"
    prob_dist = abs(calibrated_prob - 0.5)
    confidence_tag = "high" if prob_dist > 0.2 else "medium" if prob_dist > 0.1 else "low"

    # 11. JSON 로그 생성 (Generate JSON log)
    log_data = {
        "date": (pd.to_datetime(adjusted_target_date) + pd.DateOffset(days=1)).strftime('%Y-%m-%d'),
        "prob_up": float(calibrated_prob),
        "predicted_direction": predicted_direction,
        "actual_direction": actual_direction if actual_direction is not None else -1,
        "hits_last5": int(hits),
        "signal_colour": signal_colour,
        "confidence_tag": confidence_tag,
        "alpha": alpha,
        "cutoff": cutoff,
        "calibration_method": calibration_method,
        'accuracy': metrics['accuracy'],
        'precision': metrics['precision'],
        'recall': metrics['recall'],
        'f1': metrics['f1']
    }
    print(f"JSON 로그: {json.dumps(log_data, indent=4)}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python lstm_finetuning.py <TICKER> <YYYY-MM-DD>", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1]
    target_date_str = sys.argv[2]
    main(ticker, target_date_str)
