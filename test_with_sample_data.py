#!/usr/bin/env python3
"""
Test the LSTM service with generated sample data.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

def setup_sample_data():
    """Generate and save sample data for testing."""
    from src.services.generate_sample_data import save_sample_data
    
    # Create data directory if it doesn't exist
    data_dir = Path('data')
    data_dir.mkdir(exist_ok=True)
    
    print("Generating sample data...")
    price_file, volume_file = save_sample_data(
        ticker='AAPL',
        days=500,
        output_dir=str(data_dir)
    )
    
    return price_file, volume_file

def test_lstm_service():
    """Test the LSTM service with sample data."""
    print("\nTesting LSTM service with sample data...")
    
    try:
        # Import the LSTM service
        from src.services.lstm_service_clean import main
        
        # Run the service with test parameters
        import sys
        sys.argv = ['test_script.py', '--ticker', 'AAPL', '--epochs', '5']
        
        print("\nRunning LSTM service...")
        result = main()
        
        if result and result.get('status') == 'success':
            print("\n✅ LSTM service test passed!")
            print(f"Prediction: {result['prediction']}")
            return True
        else:
            print("\n❌ LSTM service test failed")
            if 'message' in result:
                print(f"Error: {result['message']}")
            return False
            
    except Exception as e:
        print(f"\n❌ Error testing LSTM service: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing LSTM Service with Sample Data")
    print("=" * 60)
    
    # Generate sample data
    price_file, volume_file = setup_sample_data()
    
    # Test the LSTM service
    success = test_lstm_service()
    
    # Print summary
    print("\n" + "=" * 60)
    if success:
        print("✅ All tests completed successfully!")
    else:
        print("❌ Some tests failed. Please check the output above for details.")
