#!/usr/bin/env python3
"""
Test script to validate the logit transformation fix for Platt scaling
This demonstrates that the fix prevents calibrated probabilities from collapsing to 0.5
"""

import numpy as np
from sklearn.linear_model import LogisticRegression

def test_logit_transformation_fix():
    """Test that logit transformation prevents Platt scaling collapse to 0.5"""
    print("🔧 Testing Logit Transformation Fix for Platt Scaling\n")
    
    # Test Case 1: Original broken approach (raw sigmoid inputs)
    print("📋 Test Case 1: Original broken approach (raw sigmoid inputs)")
    raw_predictions = np.array([0.9, 0.8, 0.7, 0.6, 0.4, 0.3, 0.2, 0.1])
    y_true = np.array([1, 1, 1, 1, 0, 0, 0, 0])  # Clear pattern
    
    # Broken approach: raw sigmoid inputs
    broken_calibrator = LogisticRegression(max_iter=1000)
    broken_calibrator.fit(raw_predictions.reshape(-1, 1), y_true)
    
    test_values = np.array([0.2, 0.5, 0.8])
    broken_preds = broken_calibrator.predict_proba(test_values.reshape(-1, 1))[:, 1]
    
    print(f"   Raw test inputs: {test_values}")
    print(f"   Broken calibrated outputs: {broken_preds}")
    print(f"   Range: {np.ptp(broken_preds):.4f}")
    
    # Test Case 2: Fixed approach (logit transformation)
    print("\n📋 Test Case 2: Fixed approach (logit transformation)")
    
    # Transform to logit space to break symmetry
    clipped = np.clip(raw_predictions, 1e-6, 1-1e-6)
    logit_x = np.log(clipped / (1 - clipped)).reshape(-1, 1)
    
    fixed_calibrator = LogisticRegression(
        solver="lbfgs",
        max_iter=1000,
        C=10.0,                   # Reduced regularization
        class_weight="balanced"   # Break symmetric label bias
    )
    fixed_calibrator.fit(logit_x, y_true)
    
    # Test predictions using logit transformation
    test_clipped = np.clip(test_values, 1e-6, 1-1e-6)
    test_logit_x = np.log(test_clipped / (1 - test_clipped)).reshape(-1, 1)
    fixed_preds = fixed_calibrator.predict_proba(test_logit_x)[:, 1]
    
    print(f"   Raw test inputs: {test_values}")
    print(f"   Fixed calibrated outputs: {fixed_preds}")
    print(f"   Range: {np.ptp(fixed_preds):.4f}")
    
    # Validation
    broken_range = np.ptp(broken_preds)
    fixed_range = np.ptp(fixed_preds)
    
    print(f"\n📊 Results:")
    print(f"   Broken approach range: {broken_range:.4f}")
    print(f"   Fixed approach range: {fixed_range:.4f}")
    
    if fixed_range > 0.1:
        print("✅ Logit transformation fix SUCCESSFUL - predictions show meaningful variation")
        return True
    else:
        print("❌ Logit transformation fix FAILED - predictions still collapsed")
        return False

def test_real_world_example():
    """Test with data similar to what LSTM models produce"""
    print("\n📋 Test Case 3: Real-world LSTM-like data")
    
    # Simulate LSTM validation data with some duplicates but clear pattern
    np.random.seed(42)
    raw_predictions = np.array([
        0.85, 0.82, 0.78, 0.75, 0.72, 0.68, 0.65, 0.62,  # High predictions
        0.45, 0.42, 0.38, 0.35, 0.32, 0.28, 0.25, 0.22   # Low predictions
    ])
    y_true = np.array([1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0])
    
    # Apply logit transformation fix
    clipped = np.clip(raw_predictions, 1e-6, 1-1e-6)
    logit_x = np.log(clipped / (1 - clipped)).reshape(-1, 1)
    
    calibrator = LogisticRegression(
        solver="lbfgs",
        max_iter=1000,
        C=10.0,
        class_weight="balanced"
    )
    calibrator.fit(logit_x, y_true)
    
    # Test with various inputs
    test_values = np.array([0.25, 0.45, 0.65, 0.85])
    test_clipped = np.clip(test_values, 1e-6, 1-1e-6)
    test_logit_x = np.log(test_clipped / (1 - test_clipped)).reshape(-1, 1)
    calibrated_preds = calibrator.predict_proba(test_logit_x)[:, 1]
    
    print(f"   Test inputs: {test_values}")
    print(f"   Calibrated outputs: {calibrated_preds}")
    print(f"   Range: {np.ptp(calibrated_preds):.4f}")
    
    # Check that predictions are monotonic (higher input = higher output for this pattern)
    is_monotonic = np.all(np.diff(calibrated_preds) > 0)
    has_variation = np.ptp(calibrated_preds) > 0.1
    
    print(f"   Monotonic: {is_monotonic}")
    print(f"   Has variation: {has_variation}")
    
    if is_monotonic and has_variation:
        print("✅ Real-world test SUCCESSFUL - calibration works properly")
        return True
    else:
        print("❌ Real-world test FAILED")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Platt Scaling Logit Transformation Fix\n")
    
    tests = [
        ("Logit Transformation Fix", test_logit_transformation_fix),
        ("Real-world Example", test_real_world_example)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Logit transformation fix is working correctly.")
        print("✅ LSTM services should now produce meaningful calibrated probabilities")
        print("✅ No more 0.5000 collapse in Platt scaling")
        return True
    else:
        print("⚠️  Some tests failed.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
