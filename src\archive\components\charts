'use client';

import React, { useState, useRef, useCallback } from 'react';

interface Msg {
  id: string;
  text: string;
  sender: 'user' | 'ai';
}

// 정규식
const YES = /(네|예|응|그래|보여|불러|show|yes|yep|sure)/i;
const NO  = /(아니|아뇨|아니요|아니오|싫|no|nope)/i;
const ASK = /[?？]$/;                  // 물음표 끝

export default function AIChat() {
  const [msgs, setMsgs]   = useState<Msg[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const containerRef     = useRef<HTMLDivElement>(null);

  /* 공통 push */
  const push = (m: Msg) => {
    setMsgs(prev => [...prev, m]);
    setTimeout(() => {
      containerRef.current?.scrollTo({ top: containerRef.current.scrollHeight, behavior: 'smooth' });
    }, 0);
  };

  /* API 호출 */
  const send = useCallback(async () => {
    if (!input.trim()) return;
    const userText = input.trim();
    push({ id:`${Date.now()}-u`, text:userText, sender:'user' });
    setInput('');
    setLoading(true);

    try {
      /* ✨ history 배열 준비 (role,content) */
      const history = msgs.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.text,
      }));

      const res = await fetch('/api/ai_chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: userText, history }),
      });
      if (!res.ok) throw new Error(`API 오류 ${res.status}`);
      const j = await res.json();

      const aiText = j.reply ?? '죄송합니다. 응답 오류가 발생했습니다.';
      push({ id:`${Date.now()}-ai`, text: aiText, sender:'ai' });
    } catch (err) {
      console.error(err);
      push({ id:`${Date.now()}-ai`, text:'서버 연결에 실패했습니다.', sender:'ai' });
    } finally {
      setLoading(false);
    }
  }, [input, msgs]);

  /* Enter 키 전송 */
  const onKey: React.KeyboardEventHandler<HTMLInputElement> = e => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      send();
    }
  };

  return (
    <div className="flex flex-col h-full border border-gray-300 rounded bg-white">
      <div ref={containerRef} className="flex-grow p-4 space-y-2 overflow-y-auto">
        {msgs.map(m => (
          <div key={m.id} className={`flex ${m.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div
              className={`px-3 py-2 rounded-lg max-w-xs lg:max-w-md ${
                m.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'
              }`}
            >
              {m.text}
            </div>
          </div>
        ))}
      </div>

      {/* 입력창 */}
      <div className="flex gap-2 p-3 border-t">
        <input
          value={input}
          onChange={e => setInput(e.target.value)}
          onKeyDown={onKey}
          disabled={loading}
          placeholder="메시지를 입력하세요…"
          className="flex-grow px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={send}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-4 rounded disabled:opacity-50"
        >
          전송
        </button>
      </div>
    </div>
  );
}
