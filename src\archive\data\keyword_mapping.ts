// final_keyword_mapping.ts
// 확장된 키워드 매핑 데이터

// 키워드 매핑 인터페이스
export interface KeywordMapping {
  keywords: string[];
  industryKey: string;
}

// 확장된 키워드 매핑 데이터
export const KEYWORD_MAPPINGS: KeywordMapping[] = [
  // 기존 키워드 매핑
  {
    keywords: ["반도체", "칩", "프로세서", "그래픽카드", "GPU", "마이크로칩", "semiconductor", "chip", "processor", "graphics card"],
    industryKey: "SEMICONDUCTORS"
  },
  {
    keywords: ["소프트웨어", "앱", "애플리케이션", "프로그램", "클라우드", "software", "app", "application", "program"],
    industryKey: "SOFTWARE"
  },
  {
    keywords: ["인공지능", "AI", "머신러닝", "딥러닝", "신경망", "artificial intelligence", "machine learning", "deep learning", "neural network"],
    industryKey: "ARTIFICIAL_INTELLIGENCE"
  },
  {
    keywords: ["자동차", "전기차", "EV", "자율주행", "오토모티브", "automotive", "electric vehicle", "autonomous", "car", "motor"],
    industryKey: "AUTOMOTIVE"
  },
  {
    keywords: ["전자상거래", "이커머스", "온라인쇼핑", "온라인 리테일", "e-commerce", "online shopping", "online retail"],
    industryKey: "ECOMMERCE"
  },
  {
    keywords: ["금융 서비스", "페이먼트", "결제", "금융 기술", "financial services", "payment", "fintech"],
    industryKey: "FINANCIAL_SERVICES"
  },
  {
    keywords: ["헬스케어", "의료", "건강", "healthcare", "medical", "health"],
    industryKey: "HEALTHCARE"
  },
  {
    keywords: ["통신", "텔레콤", "네트워크", "모바일", "무선", "telecommunications", "telecom", "network", "mobile", "wireless"],
    industryKey: "TELECOMMUNICATIONS"
  },
  {
    keywords: ["에너지", "석유", "가스", "원유", "정유", "석탄", "energy", "oil", "gas", "petroleum", "coal"],
    industryKey: "ENERGY"
  },
  {
    keywords: ["유틸리티", "전기", "수도", "가스", "공공서비스", "utilities", "electricity", "water", "gas", "public services"],
    industryKey: "UTILITIES"
  },
  {
    keywords: ["ETF", "펀드", "인덱스", "지수", "익스체인지", "fund", "index", "exchange traded fund"],
    industryKey: "ETF"
  },
  {
    keywords: ["항공", "항공사", "비행", "여객", "항공기", "airline", "aviation", "flight", "aircraft"],
    industryKey: "AIRLINES"
  },
  {
    keywords: ["소매", "리테일", "마트", "슈퍼마켓", "백화점", "retail", "mart", "supermarket", "department store"],
    industryKey: "RETAIL"
  },
  {
    keywords: ["미디어", "엔터테인먼트", "스트리밍", "방송", "영화", "음악", "media", "entertainment", "streaming", "broadcasting", "movie", "music"],
    industryKey: "MEDIA_ENTERTAINMENT"
  },
  {
    keywords: ["부동산", "리츠", "REIT", "임대", "건설", "개발", "real estate", "property", "construction", "development"],
    industryKey: "REAL_ESTATE"
  },
  
  // 추가 키워드 매핑
  {
    keywords: ["바이오테크", "생명공학", "바이오", "유전자", "biotech", "biotechnology", "gene", "genomic", "biological"],
    industryKey: "BIOTECH"
  },
  {
    keywords: ["제약", "의약품", "약품", "치료제", "pharma", "pharmaceutical", "drug", "medicine", "therapeutic"],
    industryKey: "PHARMA"
  },
  {
    keywords: ["의료기기", "의료장비", "진단기기", "medical device", "diagnostic", "healthcare equipment", "medical equipment"],
    industryKey: "MEDICAL_DEVICES"
  },
  {
    keywords: ["은행", "금융", "뱅킹", "bank", "banking", "financial", "finance", "loan", "credit"],
    industryKey: "BANKING"
  },
  {
    keywords: ["보험", "인슈어런스", "보장", "insurance", "insurer", "underwriter", "risk", "policy"],
    industryKey: "INSURANCE"
  },
  {
    keywords: ["투자", "자산관리", "펀드", "investment", "asset management", "fund", "wealth", "portfolio"],
    industryKey: "INVESTMENT_MANAGEMENT"
  },
  {
    keywords: ["클라우드", "SaaS", "PaaS", "IaaS", "cloud computing", "cloud service", "cloud platform", "cloud infrastructure"],
    industryKey: "CLOUD_COMPUTING"
  },
  {
    keywords: ["사이버보안", "보안", "해킹", "방화벽", "cybersecurity", "security", "firewall", "encryption", "protection"],
    industryKey: "CYBERSECURITY"
  },
  {
    keywords: ["재생에너지", "태양광", "풍력", "친환경에너지", "renewable", "solar", "wind", "clean energy", "green energy"],
    industryKey: "RENEWABLE_ENERGY"
  },
  {
    keywords: ["대마초", "마리화나", "카나비스", "cannabis", "marijuana", "hemp", "CBD", "THC", "weed"],
    industryKey: "CANNABIS"
  },
  {
    keywords: ["게임", "비디오게임", "게이밍", "게임개발", "gaming", "video game", "game development", "esports", "interactive entertainment"],
    industryKey: "GAMING"
  },
  {
    keywords: ["식품", "음료", "식품가공", "food", "beverage", "drink", "grocery", "snack", "packaged food"],
    industryKey: "FOOD_BEVERAGE"
  },
  {
    keywords: ["여행", "레저", "관광", "호텔", "travel", "leisure", "tourism", "hotel", "resort", "vacation"],
    industryKey: "TRAVEL_LEISURE"
  },
  {
    keywords: ["교육", "학습", "학교", "교육서비스", "education", "learning", "school", "teaching", "training", "academic"],
    industryKey: "EDUCATION"
  },
  {
    keywords: ["블록체인", "암호화폐", "비트코인", "이더리움", "blockchain", "cryptocurrency", "bitcoin", "ethereum", "token", "crypto"],
    industryKey: "BLOCKCHAIN_CRYPTO"
  },
  {
    keywords: ["우주", "항공우주", "우주탐사", "로켓", "space", "aerospace", "satellite", "rocket", "orbit", "launch"],
    industryKey: "SPACE"
  },
  {
    keywords: ["로봇", "자동화", "로보틱스", "robotics", "automation", "robot", "autonomous", "drone", "unmanned"],
    industryKey: "ROBOTICS_AUTOMATION"
  },
  {
    keywords: ["메타버스", "가상현실", "증강현실", "metaverse", "virtual reality", "augmented reality", "VR", "AR", "XR", "digital world"],
    industryKey: "METAVERSE"
  },
  {
    keywords: ["양자컴퓨팅", "양자", "quantum computing", "quantum", "qubit", "quantum technology", "quantum processor"],
    industryKey: "QUANTUM_COMPUTING"
  },
  {
    keywords: ["스팩", "기업인수목적회사", "spac", "special purpose acquisition company", "blank check", "acquisition"],
    industryKey: "SPAC"
  },
  {
    keywords: ["원자재", "광물", "금속", "채굴", "materials", "minerals", "metals", "mining", "resources"],
    industryKey: "MATERIALS"
  }
];

// 키워드로 산업군 찾기 함수
export function findIndustryByKeyword(keyword: string): string | null {
  const lowerKeyword = keyword.toLowerCase();
  
  for (const mapping of KEYWORD_MAPPINGS) {
    for (const kw of mapping.keywords) {
      if (lowerKeyword.includes(kw.toLowerCase())) {
        return mapping.industryKey;
      }
    }
  }
  
  return null;
}

// 산업군별 키워드 찾기 함수
export function findKeywordsByIndustry(industryKey: string): string[] {
  const keywords: string[] = [];
  
  for (const mapping of KEYWORD_MAPPINGS) {
    if (mapping.industryKey === industryKey) {
      keywords.push(...mapping.keywords);
    }
  }
  
  return keywords;
}
