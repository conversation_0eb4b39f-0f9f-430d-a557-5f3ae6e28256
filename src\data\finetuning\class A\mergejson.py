import json
import hashlib
from pathlib import Path
from typing import List, Dict


def list_json_files(folder: Path) -> List[Path]:
    """폴더 내 모든 .json 파일 경로 리스트(알파벳 순 정렬)."""
    return sorted(fp for fp in folder.glob("*.json") if fp.is_file())


def deduplicate_system(messages: List[Dict]) -> List[Dict]:
    """동일한 system content를 한 번만 유지."""
    seen = set()
    cleaned = []
    for m in messages:
        if m.get("role") == "system":
            key = hashlib.sha256(m.get("content", "").encode()).hexdigest()
            if key in seen:
                continue
            seen.add(key)
        cleaned.append(m)
    return cleaned


def main():
    base_dir = Path(__file__).resolve().parent      # 스크립트 위치 기준
    all_messages: List[Dict] = []

    # 원본 파일은 전혀 수정하지 않음
    for json_path in list_json_files(base_dir):
        try:
            data = json.loads(json_path.read_text(encoding="utf-8"))
            msgs = data.get("messages", [])
            all_messages.extend(deduplicate_system(msgs))
        except Exception as err:
            print(f"❌ {json_path.name}: {err}")

    # 전역 중복(system) 재검사
    all_messages = deduplicate_system(all_messages)

    # 결과 저장 (새 파일)
    output = {"messages": all_messages}
    out_file = base_dir / "merged_messages_cleaned.json"
    out_file.write_text(json.dumps(output, ensure_ascii=False, indent=2), encoding="utf-8")
    print(f"✔️ saved → {out_file.name}  ({len(all_messages)} messages)")


if __name__ == "__main__":
    main()
